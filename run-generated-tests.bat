@echo off
REM 🧪 Run Generated Tests Script (Windows)
REM This script demonstrates how to use the generated test code from the SauceLabs example

echo 🚀 SauceLabs Generated Tests Runner
echo ==================================

REM Check if generated tests directory exists
if not exist "generated-tests" (
    echo ❌ Generated tests directory not found!
    echo 📝 Please run the code generation example first:
    echo    mvn exec:java -Dexec.mainClass="com.jarvis.appium.examples.SauceLabsCodeGenerationExample"
    exit /b 1
)

echo ✅ Generated tests directory found

REM Check for Java files
dir /s /b "generated-tests\*.java" >nul 2>&1
if errorlevel 1 (
    echo ❌ No Java test files found in generated-tests\
    echo 📝 Please run the code generation example to create test files
    exit /b 1
)

echo ✅ Found generated Java test files

REM Create a temporary Maven project structure for the generated tests
echo 🔧 Setting up temporary test project...

REM Create directories
if not exist "temp-test-project\src\main\java" mkdir "temp-test-project\src\main\java"
if not exist "temp-test-project\src\test\java" mkdir "temp-test-project\src\test\java"

REM Copy generated tests
xcopy "generated-tests\*" "temp-test-project\src\test\java\" /E /I /Y >nul

REM Create a simple pom.xml for the generated tests
(
echo ^<?xml version="1.0" encoding="UTF-8"?^>
echo ^<project xmlns="http://maven.apache.org/POM/4.0.0"
echo          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
echo          xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
echo          http://maven.apache.org/xsd/maven-4.0.0.xsd"^>
echo     ^<modelVersion^>4.0.0^</modelVersion^>
echo     
echo     ^<groupId^>com.saucelabs.generated^</groupId^>
echo     ^<artifactId^>generated-tests^</artifactId^>
echo     ^<version^>1.0.0^</version^>
echo     ^<packaging^>jar^</packaging^>
echo     
echo     ^<properties^>
echo         ^<maven.compiler.source^>11^</maven.compiler.source^>
echo         ^<maven.compiler.target^>11^</maven.compiler.target^>
echo         ^<project.build.sourceEncoding^>UTF-8^</project.build.sourceEncoding^>
echo     ^</properties^>
echo     
echo     ^<dependencies^>
echo         ^<!-- Appium Java Client --^>
echo         ^<dependency^>
echo             ^<groupId^>io.appium^</groupId^>
echo             ^<artifactId^>java-client^</artifactId^>
echo             ^<version^>8.6.0^</version^>
echo         ^</dependency^>
echo         
echo         ^<!-- Selenium WebDriver --^>
echo         ^<dependency^>
echo             ^<groupId^>org.seleniumhq.selenium^</groupId^>
echo             ^<artifactId^>selenium-java^</artifactId^>
echo             ^<version^>4.15.0^</version^>
echo         ^</dependency^>
echo         
echo         ^<!-- JUnit 5 --^>
echo         ^<dependency^>
echo             ^<groupId^>org.junit.jupiter^</groupId^>
echo             ^<artifactId^>junit-jupiter^</artifactId^>
echo             ^<version^>5.10.0^</version^>
echo             ^<scope^>test^</scope^>
echo         ^</dependency^>
echo         
echo         ^<!-- TestNG ^(alternative^) --^>
echo         ^<dependency^>
echo             ^<groupId^>org.testng^</groupId^>
echo             ^<artifactId^>testng^</artifactId^>
echo             ^<version^>7.8.0^</version^>
echo             ^<scope^>test^</scope^>
echo         ^</dependency^>
echo         
echo         ^<!-- Logging --^>
echo         ^<dependency^>
echo             ^<groupId^>org.slf4j^</groupId^>
echo             ^<artifactId^>slf4j-simple^</artifactId^>
echo             ^<version^>2.0.9^</version^>
echo         ^</dependency^>
echo     ^</dependencies^>
echo     
echo     ^<build^>
echo         ^<plugins^>
echo             ^<plugin^>
echo                 ^<groupId^>org.apache.maven.plugins^</groupId^>
echo                 ^<artifactId^>maven-surefire-plugin^</artifactId^>
echo                 ^<version^>3.2.2^</version^>
echo                 ^<configuration^>
echo                     ^<includes^>
echo                         ^<include^>**/*Test.java^</include^>
echo                         ^<include^>**/*Tests.java^</include^>
echo                     ^</includes^>
echo                 ^</configuration^>
echo             ^</plugin^>
echo         ^</plugins^>
echo     ^</build^>
echo ^</project^>
) > "temp-test-project\pom.xml"

echo ✅ Temporary test project created

REM Show what was generated
echo.
echo 📋 Generated Test Structure:
echo ============================
for /r "temp-test-project\src\test\java" %%f in (*.java) do (
    echo 📄 %%~nxf
)

echo.
echo 🎯 Next Steps:
echo ==============
echo 1. 📱 Ensure your Android device/emulator is connected
echo 2. 🚀 Start the MCP server: npx jarvis-appium
echo 3. 🧪 Run the generated tests:
echo    cd temp-test-project
echo    mvn test
echo.
echo 4. 📊 View test results in: temp-test-project\target\surefire-reports\
echo.
echo 🔧 Customization:
echo ================
echo • Edit generated test files in: temp-test-project\src\test\java\
echo • Modify test data and configurations as needed
echo • Add additional test scenarios
echo • Integrate with your CI/CD pipeline
echo.
echo 🧹 Cleanup:
echo ===========
echo • Remove temp-test-project\ when done
echo • Keep generated-tests\ for reference

REM Optional: Compile the tests to check for syntax errors
echo.
echo 🔍 Validating generated code...
cd temp-test-project
mvn compile test-compile >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Generated code has compilation issues
    echo 📝 Check the generated files and dependencies
) else (
    echo ✅ Generated code compiles successfully!
)

cd ..

echo.
echo 🎉 Generated tests are ready to use!
echo 📁 Location: temp-test-project\src\test\java\

pause
