package com.jarvis.appium.cucumber.runners;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.stream.Collectors;

/**
 * Test to validate Cucumber scenarios and step definitions
 */
public class CucumberValidationTest {

    @Test
    @DisplayName("Validate feature file syntax")
    public void testFeatureFileSyntax() {
        try {
            InputStream featureFile = getClass().getClassLoader()
                .getResourceAsStream("features/SingleScenario.feature");
            
            assertNotNull(featureFile, "Feature file should exist");
            
            String content = new BufferedReader(new InputStreamReader(featureFile))
                .lines().collect(Collectors.joining("\n"));
            
            // Basic validation of Gherkin syntax
            assertTrue(content.contains("Feature:"), "Should contain Feature declaration");
            assertTrue(content.contains("Scenario:"), "Should contain at least one Scenario");
            assertTrue(content.contains("Given"), "Should contain Given steps");
            assertTrue(content.contains("When"), "Should contain When steps");
            assertTrue(content.contains("Then"), "Should contain Then steps");
            assertTrue(content.contains("@SingleScenario"), "Should contain @SingleScenario tag");
            
            System.out.println("✅ Feature file syntax is valid");
            
        } catch (Exception e) {
            fail("Failed to validate feature file: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("Validate step definitions exist")
    public void testStepDefinitionsExist() {
        try {
            // Test that step definition class exists and has required methods
            Class<?> stepsClass = Class.forName("com.jarvis.appium.cucumber.steps.SingleScenarioSteps");
            
            // Check for some key step definition methods
            boolean hasGivenMethods = java.util.Arrays.stream(stepsClass.getDeclaredMethods())
                .anyMatch(method -> method.isAnnotationPresent(io.cucumber.java.en.Given.class));
            
            boolean hasWhenMethods = java.util.Arrays.stream(stepsClass.getDeclaredMethods())
                .anyMatch(method -> method.isAnnotationPresent(io.cucumber.java.en.When.class));
            
            boolean hasThenMethods = java.util.Arrays.stream(stepsClass.getDeclaredMethods())
                .anyMatch(method -> method.isAnnotationPresent(io.cucumber.java.en.Then.class));
            
            assertTrue(hasGivenMethods, "Should have @Given step definitions");
            assertTrue(hasWhenMethods, "Should have @When step definitions");
            assertTrue(hasThenMethods, "Should have @Then step definitions");
            
            System.out.println("✅ Step definitions are properly defined");
            
        } catch (Exception e) {
            fail("Failed to validate step definitions: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("Validate TestNG runner configuration")
    public void testTestNGRunnerConfiguration() {
        try {
            Class<?> runnerClass = Class.forName("com.jarvis.appium.cucumber.runners.SingleScenarioTestNGRunner");
            
            // Check if it extends AbstractTestNGCucumberTests
            assertTrue(io.cucumber.testng.AbstractTestNGCucumberTests.class.isAssignableFrom(runnerClass),
                "Runner should extend AbstractTestNGCucumberTests");
            
            // Check if it has CucumberOptions annotation
            assertTrue(runnerClass.isAnnotationPresent(io.cucumber.testng.CucumberOptions.class),
                "Runner should have @CucumberOptions annotation");
            
            System.out.println("✅ TestNG runner is properly configured");
            
        } catch (Exception e) {
            fail("Failed to validate TestNG runner: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("Validate configuration classes")
    public void testConfigurationClasses() {
        try {
            // Test TestConfig
            Class<?> configClass = Class.forName("com.jarvis.appium.cucumber.config.TestConfig");
            
            // Check for key configuration constants
            java.lang.reflect.Field apkPathField = configClass.getDeclaredField("APK_PATH");
            java.lang.reflect.Field packageNameField = configClass.getDeclaredField("PACKAGE_NAME");
            java.lang.reflect.Field activityNameField = configClass.getDeclaredField("ACTIVITY_NAME");
            
            assertNotNull(apkPathField, "Should have APK_PATH field");
            assertNotNull(packageNameField, "Should have PACKAGE_NAME field");
            assertNotNull(activityNameField, "Should have ACTIVITY_NAME field");
            
            // Test McpUtils
            Class<?> utilsClass = Class.forName("com.jarvis.appium.cucumber.utils.McpUtils");
            
            // Check for key utility methods
            boolean hasSendMcpRequest = java.util.Arrays.stream(utilsClass.getDeclaredMethods())
                .anyMatch(method -> method.getName().equals("sendMcpRequest"));
            
            boolean hasTakeScreenshot = java.util.Arrays.stream(utilsClass.getDeclaredMethods())
                .anyMatch(method -> method.getName().equals("takeScreenshot"));
            
            assertTrue(hasSendMcpRequest, "Should have sendMcpRequest method");
            assertTrue(hasTakeScreenshot, "Should have takeScreenshot method");
            
            System.out.println("✅ Configuration classes are properly structured");
            
        } catch (Exception e) {
            fail("Failed to validate configuration classes: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("Validate cucumber properties")
    public void testCucumberProperties() {
        try {
            InputStream propsFile = getClass().getClassLoader()
                .getResourceAsStream("cucumber.properties");
            
            assertNotNull(propsFile, "cucumber.properties should exist");
            
            String content = new BufferedReader(new InputStreamReader(propsFile))
                .lines().collect(Collectors.joining("\n"));
            
            // Check for key properties
            assertTrue(content.contains("cucumber.glue"), "Should contain glue configuration");
            assertTrue(content.contains("cucumber.features"), "Should contain features configuration");
            assertTrue(content.contains("cucumber.filter.tags"), "Should contain tags configuration");
            
            System.out.println("✅ Cucumber properties are properly configured");
            
        } catch (Exception e) {
            fail("Failed to validate cucumber properties: " + e.getMessage());
        }
    }
}
