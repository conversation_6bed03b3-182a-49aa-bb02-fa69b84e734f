# 🥒 Cucumber Single Scenario Integration Guide

## 📋 Overview

This guide explains how to use the Cucumber integration for the SingleScenarioRunner functionality. The integration provides BDD-style test execution using Gherkin syntax while maintaining the same MCP-based automation capabilities.

## 🏗️ Project Structure

```
src/
├── test/
│   ├── java/
│   │   └── com/jarvis/appium/cucumber/
│   │       ├── runners/
│   │       │   ├── SingleScenarioRunner.java          # JUnit Platform runner
│   │       │   └── SingleScenarioTestNGRunner.java    # TestNG runner
│   │       ├── steps/
│   │       │   └── SingleScenarioSteps.java           # Step definitions
│   │       ├── config/
│   │       │   └── TestConfig.java                    # Test configuration
│   │       └── utils/
│   │           └── McpUtils.java                      # MCP utility methods
│   └── resources/
│       ├── features/
│       │   └── SingleScenario.feature                 # Gherkin feature file
│       └── cucumber.properties                        # Cucumber configuration
```

## 🚀 How to Run

### Option 1: Using JUnit Platform Runner (Recommended)

```bash
# Run all scenarios with @SingleScenario tag
mvn test -Dtest=SingleScenarioRunner

# Run specific scenario by name
mvn test -Dtest=SingleScenarioRunner -Dcucumber.filter.name="View Product Catalog"

# Run with specific tags
mvn test -Dtest=SingleScenarioRunner -Dcucumber.filter.tags="@ProductCatalog"
```

### Option 2: Using TestNG Runner

```bash
# Run TestNG-based Cucumber tests
mvn test -Dtest=SingleScenarioTestNGRunner

# Run with TestNG groups
mvn test -Dtest=SingleScenarioTestNGRunner -Dgroups="smoke"
```

### Option 3: Using Maven Surefire Plugin

```bash
# Run all Cucumber tests
mvn test -Dcucumber.filter.tags="@SingleScenario"

# Run specific feature file
mvn test -Dcucumber.features="src/test/resources/features/SingleScenario.feature"
```

## 📝 Available Scenarios

### 1. View Product Catalog (@ProductCatalog @Smoke)
```gherkin
Scenario: View Product Catalog
  Given the SauceLabs app is launched
  When I view the product catalog
  Then I should see Sauce Labs Backpack
  And I should see Sauce Labs Bike Light
  And I should see the sort button
```

### 2. View Product Details (@ProductDetails @Regression)
```gherkin
Scenario: View Product Details
  Given the SauceLabs app is launched
  When I view the product catalog
  And I click on "Sauce Labs Backpack"
  Then I should see the product details page
  And I should see the product name "Sauce Labs Backpack"
  And I should see the product price
  And I should see the Add To Cart button
```

### 3. Basic Navigation Test (@Navigation @Smoke)
```gherkin
Scenario: Basic Navigation Test
  Given the SauceLabs app is launched
  When I view the product catalog
  And I generate locators for current screen
  Then I should see interactive elements
  And I should be able to take a screenshot
```

### 4. Element Discovery Test (@ElementDiscovery @Debug)
```gherkin
Scenario: Element Discovery Test
  Given the SauceLabs app is launched
  When I view the product catalog
  And I generate locators for current screen
  Then I should find the username field
  And I should find navigation elements
  And I should capture the current screen state
```

## 🏷️ Available Tags

- `@SingleScenario` - Main feature tag
- `@ProductCatalog` - Product catalog related tests
- `@ProductDetails` - Product details related tests
- `@Navigation` - Navigation related tests
- `@ElementDiscovery` - Element discovery related tests
- `@Smoke` - Smoke tests (quick verification)
- `@Regression` - Regression tests (comprehensive)
- `@Debug` - Debug/troubleshooting tests

## 🔧 Configuration

### Test Configuration (TestConfig.java)
- **APK Path**: Location of the SauceLabs demo app
- **Package/Activity**: App package and activity names
- **MCP Server**: Path and command for MCP server
- **Timeouts**: Various timeout configurations
- **Device Settings**: Device name, platform, automation name

### Cucumber Configuration (cucumber.properties)
- **Plugins**: HTML, JSON, JUnit report generation
- **Glue**: Package containing step definitions
- **Features**: Location of feature files
- **Tags**: Default tag filters
- **Execution**: Parallel execution settings

## 📊 Reports

After test execution, reports are generated in:

- **HTML Report**: `target/cucumber-reports/index.html`
- **JSON Report**: `target/cucumber-reports/Cucumber.json`
- **JUnit XML**: `target/cucumber-reports/Cucumber.xml`
- **Screenshots**: `target/screenshots/` (if configured)

## 🔍 Step Definitions

### Given Steps
- `Given the SauceLabs app is launched`
- `Given the SauceLabs app is launched and ready`

### When Steps
- `When I view the product catalog`
- `When I click on "{string}"`
- `When I generate locators for current screen`

### Then Steps
- `Then I should see Sauce Labs Backpack`
- `Then I should see the product details page`
- `Then I should see the product name "{string}"`
- `Then I should see the product price`
- `Then I should see interactive elements`
- `Then I should find the username field`
- `Then I should find navigation elements`
- `Then I should capture the current screen state`

### And Steps
- `And I should see Sauce Labs Bike Light`
- `And I should see the sort button`
- `And I should see the Add To Cart button`
- `And I should be able to take a screenshot`

## 🚨 Prerequisites

1. **Android Device/Emulator**: Connected and ready
   ```bash
   adb devices
   ```

2. **SauceLabs APK**: Available at configured path
   ```
   C:\POC\AI\MCP Integration Server\mcp-client-integration\Android.SauceLabs.Mobile.Sample.app.2.7.1.apk
   ```

3. **MCP Server**: Accessible at configured location
   ```
   C:\POC\AI\MCP Integration Server\jarvis-appium
   ```

4. **Java 11+**: Required for compilation and execution

5. **Maven**: For dependency management and test execution

## 🎯 Benefits of Cucumber Integration

### ✅ **BDD Approach**
- **Natural Language**: Tests written in plain English
- **Stakeholder Friendly**: Non-technical team members can understand
- **Living Documentation**: Features serve as documentation

### ✅ **Reusable Steps**
- **Step Definitions**: Reusable across multiple scenarios
- **Parameterized Steps**: Dynamic values using step parameters
- **Background Steps**: Common setup across scenarios

### ✅ **Flexible Execution**
- **Tag-based Filtering**: Run specific test categories
- **Multiple Runners**: JUnit Platform and TestNG support
- **Parallel Execution**: Configurable parallel test execution

### ✅ **Rich Reporting**
- **Multiple Formats**: HTML, JSON, JUnit XML reports
- **Screenshots**: Automatic screenshot capture
- **Detailed Logs**: Comprehensive test execution logs

## 🔄 Comparison with Original SingleScenarioRunner

| Aspect | Original SingleScenarioRunner | Cucumber Integration |
|--------|-------------------------------|---------------------|
| **Test Format** | Java code | Gherkin scenarios |
| **Readability** | Technical | Business-friendly |
| **Reusability** | Limited | High (step definitions) |
| **Reporting** | Basic logging | Rich HTML/JSON reports |
| **Execution** | Single main method | Multiple runners/tags |
| **Maintenance** | Code changes required | Feature file updates |
| **Collaboration** | Developer-focused | Team-friendly |

## 🎮 Quick Start

1. **Ensure prerequisites are met**
2. **Run a simple test**:
   ```bash
   mvn test -Dtest=SingleScenarioRunner -Dcucumber.filter.tags="@Smoke"
   ```
3. **View the HTML report**:
   ```
   target/cucumber-reports/index.html
   ```
4. **Add your own scenarios** to `SingleScenario.feature`
5. **Extend step definitions** in `SingleScenarioSteps.java`

## 🔧 Customization

### Adding New Scenarios
1. Add scenario to `SingleScenario.feature`
2. Implement missing step definitions in `SingleScenarioSteps.java`
3. Use appropriate tags for categorization

### Adding New Step Definitions
1. Create new step methods in `SingleScenarioSteps.java`
2. Use MCP utility methods from `McpUtils.java`
3. Follow existing patterns for consistency

### Modifying Configuration
1. Update `TestConfig.java` for app/device settings
2. Modify `cucumber.properties` for Cucumber settings
3. Adjust timeouts and paths as needed

The Cucumber integration provides a powerful, flexible, and maintainable way to execute the SingleScenarioRunner functionality while enabling better collaboration and reporting capabilities.
