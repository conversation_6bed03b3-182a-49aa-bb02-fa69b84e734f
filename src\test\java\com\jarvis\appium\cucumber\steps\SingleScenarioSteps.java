package com.jarvis.appium.cucumber.steps;

import io.cucumber.java.en.Given;
import io.cucumber.java.en.When;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.And;
import io.cucumber.java.Before;
import io.cucumber.java.After;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.concurrent.TimeUnit;

/**
 * Step definitions for Single Scenario Cucumber tests
 * Based on the SingleScenarioRunner functionality
 */
public class SingleScenarioSteps {

    private static final Logger logger = LoggerFactory.getLogger(SingleScenarioSteps.class);

    // Configuration - using correct package info
    private static final String APK_PATH = "C:\\POC\\AI\\MCP Integration Server\\mcp-client-integration\\Android.SauceLabs.Mobile.Sample.app.2.7.1.apk";
    private static final String PACKAGE_NAME = "com.swaglabsmobileapp";
    private static final String ACTIVITY_NAME = "com.swaglabsmobileapp.SplashActivity";

    private static BufferedWriter writer;
    private static BufferedReader reader;
    private static BufferedReader errorReader;
    private static Process mcpProcess;
    private static int requestId = 1;
    private static boolean mcpInitialized = false;

    @Before
    public void setUp() throws Exception {
        if (!mcpInitialized) {
            logger.info("🚀 Setting up MCP server for Cucumber test...");
            initializeMcp();
            mcpInitialized = true;
        }
    }

    @After
    public void tearDown() throws Exception {
        // Keep MCP running for subsequent scenarios
        logger.info("📸 Taking final screenshot after scenario...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(2000);
    }

    // ============================================================================
    // GIVEN STEPS
    // ============================================================================

    @Given("the SauceLabs app is launched")
    public void theSauceLabsAppIsLaunched() throws Exception {
        logger.info("📋 GIVEN: the SauceLabs app is launched");
        logger.info("   📸 Taking screenshot to verify app is launched...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(3000);
    }

    @Given("the SauceLabs app is launched and ready")
    public void theSauceLabsAppIsLaunchedAndReady() throws Exception {
        logger.info("📋 GIVEN: the SauceLabs app is launched and ready");
        logger.info("   ⏳ Ensuring app is fully loaded...");
        Thread.sleep(2000);
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(3000);
    }

    // ============================================================================
    // WHEN STEPS
    // ============================================================================

    @When("I view the product catalog")
    public void iViewTheProductCatalog() throws Exception {
        logger.info("📋 WHEN: I view the product catalog");
        logger.info("   🔍 Generating locators for current screen...");
        sendMcpRequest("tools/call", "{\"name\":\"generate_locators\",\"arguments\":{}}");
        Thread.sleep(5000);
    }

    @When("I click on {string}")
    public void iClickOn(String elementName) throws Exception {
        logger.info("📋 WHEN: I click on '{}'", elementName);
        logger.info("   🖱️ Attempting to click on element...");
        
        // Try to find and click the element using accessibility ID first
        String selector = "//android.widget.TextView[@text='" + elementName + "']";
        sendMcpRequest("tools/call", 
            "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"" + selector + "\"}}");
        Thread.sleep(2000);
        
        // Click the element (assuming it was found)
        sendMcpRequest("tools/call", 
            "{\"name\":\"appium_click\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"" + selector + "\"}}");
        Thread.sleep(3000);
    }

    @When("I generate locators for current screen")
    public void iGenerateLocatorsForCurrentScreen() throws Exception {
        logger.info("📋 WHEN: I generate locators for current screen");
        logger.info("   🔍 Generating locators...");
        sendMcpRequest("tools/call", "{\"name\":\"generate_locators\",\"arguments\":{}}");
        Thread.sleep(5000);
    }

    // ============================================================================
    // THEN STEPS
    // ============================================================================

    @Then("I should see Sauce Labs Backpack")
    public void iShouldSeeSauceLabsBackpack() throws Exception {
        logger.info("📋 THEN: I should see Sauce Labs Backpack");
        logger.info("   🔎 Looking for 'Sauce Labs Backpack' element...");
        sendMcpRequest("tools/call",
            "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[contains(@text,'Sauce Labs Backpack')]\"}}}");
        Thread.sleep(3000);
    }

    @Then("I should see the product details page")
    public void iShouldSeeTheProductDetailsPage() throws Exception {
        logger.info("📋 THEN: I should see the product details page");
        logger.info("   📸 Taking screenshot of product details...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(3000);
    }

    @Then("I should see the product name {string}")
    public void iShouldSeeTheProductName(String productName) throws Exception {
        logger.info("📋 THEN: I should see the product name '{}'", productName);
        logger.info("   🔎 Verifying product name is displayed...");
        sendMcpRequest("tools/call",
            "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[contains(@text,'" + productName + "')]\"}}}");
        Thread.sleep(3000);
    }

    @Then("I should see the product price")
    public void iShouldSeeTheProductPrice() throws Exception {
        logger.info("📋 THEN: I should see the product price");
        logger.info("   💰 Looking for price element...");
        sendMcpRequest("tools/call",
            "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[contains(@text,'$') or contains(@text,'price')]\"}}}");
        Thread.sleep(3000);
    }

    @Then("I should see interactive elements")
    public void iShouldSeeInteractiveElements() throws Exception {
        logger.info("📋 THEN: I should see interactive elements");
        logger.info("   🎯 Verifying interactive elements are present...");
        // This step is validated by the successful locator generation
        Thread.sleep(2000);
    }

    @Then("I should find the username field")
    public void iShouldFindTheUsernameField() throws Exception {
        logger.info("📋 THEN: I should find the username field");
        logger.info("   🔎 Looking for username field...");
        sendMcpRequest("tools/call",
            "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//android.widget.EditText[@content-desc='test-Username']\"}}");
        Thread.sleep(3000);
    }

    @Then("I should find navigation elements")
    public void iShouldFindNavigationElements() throws Exception {
        logger.info("📋 THEN: I should find navigation elements");
        logger.info("   🧭 Looking for navigation elements...");
        sendMcpRequest("tools/call",
            "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[contains(@content-desc,'menu') or contains(@text,'Menu')]\"}}}");
        Thread.sleep(3000);
    }

    @Then("I should capture the current screen state")
    public void iShouldCaptureTheCurrentScreenState() throws Exception {
        logger.info("📋 THEN: I should capture the current screen state");
        logger.info("   📸 Capturing screen state...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(3000);
    }

    // ============================================================================
    // AND STEPS
    // ============================================================================

    @And("I should see Sauce Labs Bike Light")
    public void iShouldSeeSauceLabsBikeLight() throws Exception {
        logger.info("📋 AND: I should see Sauce Labs Bike Light");
        logger.info("   🔎 Looking for 'Sauce Labs Bike Light' element...");
        sendMcpRequest("tools/call",
            "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[contains(@text,'Sauce Labs Bike Light')]\"}}}");
        Thread.sleep(3000);
    }

    @And("I should see the sort button")
    public void iShouldSeeTheSortButton() throws Exception {
        logger.info("📋 AND: I should see the sort button");
        logger.info("   🔎 Looking for sort button...");
        sendMcpRequest("tools/call",
            "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[contains(@content-desc,'sort') or contains(@text,'sort') or contains(@text,'Sort')]\"}}}");
        Thread.sleep(3000);
    }

    @And("I should see the Add To Cart button")
    public void iShouldSeeTheAddToCartButton() throws Exception {
        logger.info("📋 AND: I should see the Add To Cart button");
        logger.info("   🛒 Looking for Add To Cart button...");
        sendMcpRequest("tools/call",
            "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[contains(@text,'ADD TO CART') or contains(@content-desc,'Add To Cart')]\"}}}");
        Thread.sleep(3000);
    }

    @And("I should be able to take a screenshot")
    public void iShouldBeAbleToTakeAScreenshot() throws Exception {
        logger.info("📋 AND: I should be able to take a screenshot");
        logger.info("   📸 Taking verification screenshot...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(3000);
    }

    // ============================================================================
    // HELPER METHODS
    // ============================================================================

    private void initializeMcp() throws IOException, InterruptedException {
        logger.info("🚀 Starting MCP server...");

        // Start MCP server
        String[] command = { "node", "dist/index.js" };
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.directory(new File("C:\\POC\\AI\\MCP Integration Server\\jarvis-appium"));
        processBuilder.redirectErrorStream(false);

        mcpProcess = processBuilder.start();

        writer = new BufferedWriter(new OutputStreamWriter(mcpProcess.getOutputStream()));
        reader = new BufferedReader(new InputStreamReader(mcpProcess.getInputStream()));
        errorReader = new BufferedReader(new InputStreamReader(mcpProcess.getErrorStream()));

        // Start output readers
        startOutputReaders();
        Thread.sleep(3000);

        // Initialize MCP
        logger.info("🔗 Initializing MCP connection...");
        sendMcpRequest("initialize",
            "{\"protocolVersion\":\"2024-11-05\",\"clientInfo\":{\"name\":\"cucumber-single-scenario\",\"version\":\"1.0.0\"},\"capabilities\":{}}");
        Thread.sleep(2000);

        // Select Android platform
        logger.info("📱 Selecting Android platform...");
        sendMcpRequest("tools/call", "{\"name\":\"select_platform\",\"arguments\":{\"platform\":\"android\"}}");
        Thread.sleep(2000);

        // Create session
        logger.info("📲 Creating session with SauceLabs app...");
        String sessionCapabilities = "{"
            + "\"platform\":\"android\","
            + "\"capabilities\":{"
            + "\"platformName\":\"Android\","
            + "\"deviceName\":\"emulator-5554\","
            + "\"app\":\"" + APK_PATH.replace("\\", "\\\\") + "\","
            + "\"appPackage\":\"" + PACKAGE_NAME + "\","
            + "\"appActivity\":\"" + ACTIVITY_NAME + "\","
            + "\"automationName\":\"UiAutomator2\","
            + "\"newCommandTimeout\":300,"
            + "\"autoGrantPermissions\":true,"
            + "\"noReset\":false,"
            + "\"fullReset\":false"
            + "}"
            + "}";

        sendMcpRequest("tools/call", "{\"name\":\"create_session\",\"arguments\":" + sessionCapabilities + "}");

        logger.info("⏳ Waiting for app to launch (20 seconds)...");
        Thread.sleep(20000); // Wait for app to fully launch
    }

    private void startOutputReaders() {
        Thread outputThread = new Thread(() -> {
            try {
                String line;
                while ((line = reader.readLine()) != null) {
                    logger.info("📥 MCP: {}", line);
                }
            } catch (IOException e) {
                logger.debug("Output reader finished");
            }
        });

        Thread errorThread = new Thread(() -> {
            try {
                String line;
                while ((line = errorReader.readLine()) != null) {
                    logger.debug("🔧 MCP Debug: {}", line);
                }
            } catch (IOException e) {
                logger.debug("Error reader finished");
            }
        });

        outputThread.setDaemon(true);
        errorThread.setDaemon(true);
        outputThread.start();
        errorThread.start();
    }

    private void sendMcpRequest(String method, String params) throws IOException {
        String request = "{\"jsonrpc\":\"2.0\",\"id\":\"" + (requestId++) + "\",\"method\":\"" + method
            + "\",\"params\":" + params + "}";
        logger.debug("📤 Sending: {}", request);
        writer.write(request);
        writer.newLine();
        writer.flush();
    }
}
