# 🧪 SauceLabs BDD Test Scenarios for Code Generation
# These scenarios are used by the SauceLabsCodeGenerationExample to generate test code

# ============================================================================
# 🛍️ PRODUCT CATALOG SCENARIOS
# ============================================================================

Scenario: Browse Product Catalog
Given the SauceLabs app is launched
When I view the product catalog
And I click on Sauce Labs Backpack
Then I should see product details
And I should see the product price
And I should see the Add To Cart button

Scenario: View Product Details
Given the SauceLabs app is launched
When I click on Sauce Labs Backpack
Then I should see product details
And I should see the product name "Sauce Labs Backpack"
And I should see the product price "$29.99"
And I should see the product description
And I should see the Add To Cart button

# ============================================================================
# 🛒 SHOPPING CART SCENARIOS
# ============================================================================

Scenario: Add Product to Cart
Given the SauceLabs app is launched
When I click on Sauce Labs Backpack
And I click the Add To Cart button
Then the cart badge should show 1 item
And I should see the cart icon updated

Scenario: Add Multiple Products to Cart
Given the SauceLabs app is launched
When I click on Sauce Labs Backpack
And I click the Add To Cart button
And I click on Sauce Labs Bike Light
And I click the Add To Cart button
Then the cart badge should show 2 items

Scenario: Manage Shopping Cart
Given I have added Sauce Labs Backpack to cart
When I click on cart badge
And I view the cart contents
Then I should see the product in cart
And I should see the correct quantity
And I should see the total price

Scenario: Remove Item from Cart
Given I have added Sauce Labs Backpack to cart
When I click on cart badge
And I click remove item
Then the cart should be empty
And the cart badge should show 0 items

# ============================================================================
# 🔐 LOGIN SCENARIOS
# ============================================================================

Scenario: Valid User Login
Given the SauceLabs app is launched
When I open the menu
And I navigate to login
And I enter username 'standard_user'
And I enter password 'secret_sauce'
And I click the login button
Then I should be logged in successfully
And I should see the products page

Scenario: Invalid User Login
Given the SauceLabs app is launched
When I open the menu
And I navigate to login
And I enter username 'invalid_user'
And I enter password 'wrong_password'
And I click the login button
Then I should see an error message
And I should remain on the login screen

Scenario: Locked Out User Login
Given the SauceLabs app is launched
When I open the menu
And I navigate to login
And I enter username 'locked_out_user'
And I enter password 'secret_sauce'
And I click the login button
Then I should see a locked out error message
And I should remain on the login screen

Scenario: Empty Credentials Login
Given the SauceLabs app is launched
When I open the menu
And I navigate to login
And I click the login button
Then I should see validation error messages
And I should remain on the login screen

# ============================================================================
# 🎯 END-TO-END SCENARIOS
# ============================================================================

Scenario: Complete Shopping Flow
Given the SauceLabs app is launched
When I click on Sauce Labs Backpack
And I click the Add To Cart button
And I click on Sauce Labs Bike Light
And I click the Add To Cart button
And I click on cart badge
Then I should see 2 items in cart
And I should see the correct total price
And I should be able to proceed to checkout

Scenario: Shopping with Login
Given the SauceLabs app is launched
When I open the menu
And I navigate to login
And I enter username 'standard_user'
And I enter password 'secret_sauce'
And I click the login button
And I click on Sauce Labs Backpack
And I click the Add To Cart button
Then the cart badge should show 1 item
And I should remain logged in

# ============================================================================
# 📊 UI INTERACTION SCENARIOS
# ============================================================================

Scenario: Sort Products
Given the SauceLabs app is launched
When I click the sort button
And I select sort by name A to Z
Then products should be sorted alphabetically
And Sauce Labs Backpack should appear first

Scenario: Sort Products by Price
Given the SauceLabs app is launched
When I click the sort button
And I select sort by price low to high
Then products should be sorted by price ascending
And the cheapest product should appear first

Scenario: Filter Products
Given the SauceLabs app is launched
When I click the filter button
And I select filter by category
Then only products in selected category should be visible

# ============================================================================
# 🔄 NAVIGATION SCENARIOS
# ============================================================================

Scenario: Navigate Menu Options
Given the SauceLabs app is launched
When I open the menu
Then I should see menu options
And I should see "All Items" option
And I should see "About" option
And I should see "Logout" option
And I should see "Reset App State" option

Scenario: About Page Navigation
Given the SauceLabs app is launched
When I open the menu
And I click on "About" option
Then I should see the about page
And I should see app information

Scenario: Reset App State
Given the SauceLabs app is launched
And I have added items to cart
When I open the menu
And I click on "Reset App State"
Then the cart should be empty
And the app should return to initial state

# ============================================================================
# 🚨 ERROR HANDLING SCENARIOS
# ============================================================================

Scenario: Handle Network Error
Given the SauceLabs app is launched
When network connectivity is lost
And I try to perform an action
Then I should see appropriate error message
And the app should handle the error gracefully

Scenario: Handle App Crash Recovery
Given the SauceLabs app has crashed
When I relaunch the app
Then the app should start normally
And previous state should be recovered if possible

# ============================================================================
# 📱 DEVICE SPECIFIC SCENARIOS
# ============================================================================

Scenario: Portrait Orientation
Given the SauceLabs app is launched
When the device is in portrait orientation
Then all elements should be properly displayed
And navigation should work correctly

Scenario: Landscape Orientation
Given the SauceLabs app is launched
When I rotate the device to landscape
Then all elements should adapt to landscape layout
And functionality should remain intact

# ============================================================================
# 🎨 ACCESSIBILITY SCENARIOS
# ============================================================================

Scenario: Screen Reader Support
Given the SauceLabs app is launched
When screen reader is enabled
Then all elements should have proper accessibility labels
And navigation should work with screen reader

Scenario: High Contrast Mode
Given the SauceLabs app is launched
When high contrast mode is enabled
Then all text should remain readable
And UI elements should have sufficient contrast

# ============================================================================
# 📊 PERFORMANCE SCENARIOS
# ============================================================================

Scenario: App Launch Performance
Given the SauceLabs app is not running
When I launch the app
Then the app should start within 5 seconds
And the splash screen should be displayed

Scenario: Page Load Performance
Given the SauceLabs app is launched
When I navigate between pages
Then each page should load within 3 seconds
And transitions should be smooth

# ============================================================================
# 💾 DATA PERSISTENCE SCENARIOS
# ============================================================================

Scenario: Cart Persistence
Given I have added items to cart
When I close and reopen the app
Then the cart items should be preserved
And the cart count should be correct

Scenario: Login State Persistence
Given I am logged in
When I close and reopen the app
Then I should remain logged in
And my session should be maintained
