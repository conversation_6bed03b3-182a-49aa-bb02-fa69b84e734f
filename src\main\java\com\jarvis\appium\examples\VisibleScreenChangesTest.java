package com.jarvis.appium.examples;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.concurrent.TimeUnit;

/**
 * Test that makes VISIBLE changes to the Android emulator screen
 */
public class VisibleScreenChangesTest {
    
    private static final Logger logger = LoggerFactory.getLogger(VisibleScreenChangesTest.class);
    
    private static BufferedWriter writer;
    private static BufferedReader reader;
    private static BufferedReader errorReader;
    
    public static void main(String[] args) {
        try {
            testVisibleScreenChanges();
        } catch (Exception e) {
            logger.error("Visible screen changes test failed", e);
        }
    }
    
    private static void testVisibleScreenChanges() throws IOException, InterruptedException {
        logger.info("=== 📱 VISIBLE Screen Changes Test ===");
        logger.info("🎬 Watch your Android emulator - you'll see real interactions!");
        
        // Start MCP server
        String[] command = {"node", "dist/index.js"};
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.directory(new File("C:\\POC\\AI\\MCP Integration Server\\jarvis-appium"));
        processBuilder.redirectErrorStream(false);
        
        logger.info("🚀 Starting MCP server...");
        Process process = processBuilder.start();
        
        writer = new BufferedWriter(new OutputStreamWriter(process.getOutputStream()));
        reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
        
        // Start output readers
        startOutputReaders();
        Thread.sleep(3000);
        
        // Initialize MCP
        logger.info("🔗 Initializing MCP connection...");
        sendMcpRequest("initialize", "{\"protocolVersion\":\"2024-11-05\",\"clientInfo\":{\"name\":\"visible-changes-test\",\"version\":\"1.0.0\"},\"capabilities\":{}}");
        Thread.sleep(2000);
        
        // Select Android platform
        logger.info("📱 Selecting Android platform...");
        sendMcpRequest("tools/call", "{\"name\":\"select_platform\",\"arguments\":{\"platform\":\"android\"}}");
        Thread.sleep(2000);
        
        // Create session
        logger.info("🎯 Creating Android session...");
        String sessionCapabilities = "{\"platform\":\"android\",\"capabilities\":{\"platformName\":\"Android\",\"deviceName\":\"emulator-5554\",\"automationName\":\"UiAutomator2\"}}";
        sendMcpRequest("tools/call", "{\"name\":\"create_session\",\"arguments\":" + sessionCapabilities + "}");
        Thread.sleep(10000);
        
        logger.info("📸 Taking initial screenshot...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(3000);
        
        // Step 1: Find and click the first clickable element
        logger.info("🎯 STEP 1: Finding first clickable element...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@clickable='true'][1]\"}}");
        Thread.sleep(3000);
        
        logger.info("👆 CLICKING the first clickable element - WATCH YOUR SCREEN!");
        sendMcpRequest("tools/call", "{\"name\":\"appium_click\",\"arguments\":{\"elementUUID\":\"00000000-0000-012d-ffff-ffff0000002b\"}}");
        Thread.sleep(5000); // Wait to see the change
        
        logger.info("📸 Taking screenshot after first click...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(3000);
        
        // Step 2: Try to find and click another element
        logger.info("🎯 STEP 2: Finding another clickable element...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@clickable='true'][2]\"}}");
        Thread.sleep(3000);
        
        logger.info("👆 CLICKING the second clickable element - WATCH YOUR SCREEN!");
        sendMcpRequest("tools/call", "{\"name\":\"appium_click\",\"arguments\":{\"elementUUID\":\"PLACEHOLDER_ELEMENT_ID\"}}");
        Thread.sleep(5000); // Wait to see the change
        
        // Step 3: Try to open notification panel by swiping down
        logger.info("🎯 STEP 3: Looking for status bar to swipe down...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@resource-id='com.android.systemui:id/status_bar']\"}}");
        Thread.sleep(3000);
        
        // Step 4: Try to find Settings app or any app icon
        logger.info("🎯 STEP 4: Looking for app icons...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@text='Settings' or @content-desc='Settings' or contains(@text, 'App')]\"}}");
        Thread.sleep(3000);
        
        // Step 5: Try to find and click home button
        logger.info("🎯 STEP 5: Looking for home button...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@content-desc='Home' or @resource-id='android:id/home']\"}}");
        Thread.sleep(3000);
        
        logger.info("📸 Taking final screenshot...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(3000);
        
        logger.info("✅ VISIBLE SCREEN CHANGES TEST COMPLETED!");
        logger.info("🎬 You should have seen your Android emulator screen change during this test!");
        
        // Clean up
        logger.info("🧹 Cleaning up...");
        writer.close();
        reader.close();
        errorReader.close();
        process.destroy();
    }
    
    private static void sendMcpRequest(String method, String params) throws IOException {
        String request = "{\"jsonrpc\":\"2.0\",\"id\":\"" + System.currentTimeMillis() + "\",\"method\":\"" + method + "\",\"params\":" + params + "}";
        logger.info("📤 SENDING: {}", request);
        writer.write(request);
        writer.newLine();
        writer.flush();
    }
    
    private static void startOutputReaders() {
        Thread outputThread = new Thread(() -> {
            try {
                String line;
                while ((line = reader.readLine()) != null) {
                    // Show important responses but filter out base64 screenshot data
                    if (line.contains("Element id") || line.contains("session created") || line.contains("clicked")) {
                        logger.info("📥 SERVER: {}", line);
                    } else if (line.contains("screenshot") && line.contains("\"result\"")) {
                        // Don't log screenshot responses as they contain large base64 data
                        logger.info("📸 Screenshot taken successfully");
                    } else if (line.contains("\"result\"") && !line.contains("data:image") && line.length() < 500) {
                        // Only log short result messages, avoid base64 data
                        logger.info("📥 SERVER: {}", line);
                    }
                }
            } catch (IOException e) {
                // Expected when process ends
            }
        });
        outputThread.setDaemon(true);
        outputThread.start();

        Thread errorThread = new Thread(() -> {
            try {
                String line;
                while ((line = errorReader.readLine()) != null) {
                    // Only show important errors
                    if (line.contains("ERROR") && !line.contains("DEBUG") && !line.contains("dbug")) {
                        logger.error("⚠️ SERVER ERROR: {}", line);
                    }
                }
            } catch (IOException e) {
                // Expected when process ends
            }
        });
        errorThread.setDaemon(true);
        errorThread.start();
    }
}
