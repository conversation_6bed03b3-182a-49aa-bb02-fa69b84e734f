# Cucumber Configuration Properties
# Configuration for Single Scenario Cucumber Tests

# Cucumber Engine Configuration
cucumber.publish.enabled=false
cucumber.publish.quiet=true

# Plugin Configuration
cucumber.plugin=pretty,html:target/cucumber-reports,json:target/cucumber-reports/Cucumber.json,junit:target/cucumber-reports/Cucumber.xml

# Glue Configuration
cucumber.glue=com.jarvis.appium.cucumber.steps

# Features Configuration
cucumber.features=src/test/resources/features

# Filter Configuration
cucumber.filter.tags=@SingleScenario

# Execution Configuration
cucumber.execution.parallel.enabled=false
cucumber.execution.parallel.mode.default=same_thread

# Snippet Configuration
cucumber.snippet-type=camelcase

# Object Factory Configuration
cucumber.object-factory=cucumber.runtime.java.DefaultJavaObjectFactory

# Logging Configuration
cucumber.logging.level=INFO
