package com.jarvis.appium.cucumber.runners;

import com.jarvis.appium.cucumber.steps.SingleScenarioSteps;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

import java.lang.reflect.Method;

/**
 * Mock test to verify step definitions can be called without MCP server
 */
public class MockCucumberTest {

    private SingleScenarioSteps steps;

    @BeforeEach
    public void setUp() {
        steps = new SingleScenarioSteps();
    }

    @Test
    @DisplayName("Test step definitions can be instantiated")
    public void testStepDefinitionsInstantiation() {
        assertNotNull(steps, "Step definitions should be instantiable");
        System.out.println("✅ Step definitions instantiated successfully");
    }

    @Test
    @DisplayName("Test Given step methods exist")
    public void testGivenStepMethods() {
        try {
            // Check for Given step methods
            Method givenAppLaunched = steps.getClass().getDeclaredMethod("theSauceLabsAppIsLaunched");
            Method givenAppReady = steps.getClass().getDeclaredMethod("theSauceLabsAppIsLaunchedAndReady");
            
            assertNotNull(givenAppLaunched, "Should have theSauceLabsAppIsLaunched method");
            assertNotNull(givenAppReady, "Should have theSauceLabsAppIsLaunchedAndReady method");
            
            // Check annotations
            assertTrue(givenAppLaunched.isAnnotationPresent(io.cucumber.java.en.Given.class),
                "Method should have @Given annotation");
            assertTrue(givenAppReady.isAnnotationPresent(io.cucumber.java.en.Given.class),
                "Method should have @Given annotation");
            
            System.out.println("✅ Given step methods are properly defined");
            
        } catch (Exception e) {
            fail("Failed to validate Given step methods: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("Test When step methods exist")
    public void testWhenStepMethods() {
        try {
            // Check for When step methods
            Method whenViewCatalog = steps.getClass().getDeclaredMethod("iViewTheProductCatalog");
            Method whenClickOn = steps.getClass().getDeclaredMethod("iClickOn", String.class);
            Method whenGenerateLocators = steps.getClass().getDeclaredMethod("iGenerateLocatorsForCurrentScreen");
            
            assertNotNull(whenViewCatalog, "Should have iViewTheProductCatalog method");
            assertNotNull(whenClickOn, "Should have iClickOn method");
            assertNotNull(whenGenerateLocators, "Should have iGenerateLocatorsForCurrentScreen method");
            
            // Check annotations
            assertTrue(whenViewCatalog.isAnnotationPresent(io.cucumber.java.en.When.class),
                "Method should have @When annotation");
            assertTrue(whenClickOn.isAnnotationPresent(io.cucumber.java.en.When.class),
                "Method should have @When annotation");
            assertTrue(whenGenerateLocators.isAnnotationPresent(io.cucumber.java.en.When.class),
                "Method should have @When annotation");
            
            System.out.println("✅ When step methods are properly defined");
            
        } catch (Exception e) {
            fail("Failed to validate When step methods: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("Test Then step methods exist")
    public void testThenStepMethods() {
        try {
            // Check for Then step methods
            Method thenSeeBackpack = steps.getClass().getDeclaredMethod("iShouldSeeSauceLabsBackpack");
            Method thenSeeProductDetails = steps.getClass().getDeclaredMethod("iShouldSeeTheProductDetailsPage");
            Method thenSeeProductName = steps.getClass().getDeclaredMethod("iShouldSeeTheProductName", String.class);
            Method thenSeePrice = steps.getClass().getDeclaredMethod("iShouldSeeTheProductPrice");
            
            assertNotNull(thenSeeBackpack, "Should have iShouldSeeSauceLabsBackpack method");
            assertNotNull(thenSeeProductDetails, "Should have iShouldSeeTheProductDetailsPage method");
            assertNotNull(thenSeeProductName, "Should have iShouldSeeTheProductName method");
            assertNotNull(thenSeePrice, "Should have iShouldSeeTheProductPrice method");
            
            // Check annotations
            assertTrue(thenSeeBackpack.isAnnotationPresent(io.cucumber.java.en.Then.class),
                "Method should have @Then annotation");
            assertTrue(thenSeeProductDetails.isAnnotationPresent(io.cucumber.java.en.Then.class),
                "Method should have @Then annotation");
            assertTrue(thenSeeProductName.isAnnotationPresent(io.cucumber.java.en.Then.class),
                "Method should have @Then annotation");
            assertTrue(thenSeePrice.isAnnotationPresent(io.cucumber.java.en.Then.class),
                "Method should have @Then annotation");
            
            System.out.println("✅ Then step methods are properly defined");
            
        } catch (Exception e) {
            fail("Failed to validate Then step methods: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("Test And step methods exist")
    public void testAndStepMethods() {
        try {
            // Check for And step methods
            Method andSeeBikeLight = steps.getClass().getDeclaredMethod("iShouldSeeSauceLabsBikeLight");
            Method andSeeSortButton = steps.getClass().getDeclaredMethod("iShouldSeeTheSortButton");
            Method andSeeAddToCart = steps.getClass().getDeclaredMethod("iShouldSeeTheAddToCartButton");
            Method andTakeScreenshot = steps.getClass().getDeclaredMethod("iShouldBeAbleToTakeAScreenshot");
            
            assertNotNull(andSeeBikeLight, "Should have iShouldSeeSauceLabsBikeLight method");
            assertNotNull(andSeeSortButton, "Should have iShouldSeeTheSortButton method");
            assertNotNull(andSeeAddToCart, "Should have iShouldSeeTheAddToCartButton method");
            assertNotNull(andTakeScreenshot, "Should have iShouldBeAbleToTakeAScreenshot method");
            
            // Check annotations
            assertTrue(andSeeBikeLight.isAnnotationPresent(io.cucumber.java.en.And.class),
                "Method should have @And annotation");
            assertTrue(andSeeSortButton.isAnnotationPresent(io.cucumber.java.en.And.class),
                "Method should have @And annotation");
            assertTrue(andSeeAddToCart.isAnnotationPresent(io.cucumber.java.en.And.class),
                "Method should have @And annotation");
            assertTrue(andTakeScreenshot.isAnnotationPresent(io.cucumber.java.en.And.class),
                "Method should have @And annotation");
            
            System.out.println("✅ And step methods are properly defined");
            
        } catch (Exception e) {
            fail("Failed to validate And step methods: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("Test Before and After hooks exist")
    public void testHookMethods() {
        try {
            // Check for Before and After hooks
            Method setUp = steps.getClass().getDeclaredMethod("setUp");
            Method tearDown = steps.getClass().getDeclaredMethod("tearDown");
            
            assertNotNull(setUp, "Should have setUp method");
            assertNotNull(tearDown, "Should have tearDown method");
            
            // Check annotations
            assertTrue(setUp.isAnnotationPresent(io.cucumber.java.Before.class),
                "setUp method should have @Before annotation");
            assertTrue(tearDown.isAnnotationPresent(io.cucumber.java.After.class),
                "tearDown method should have @After annotation");
            
            System.out.println("✅ Hook methods are properly defined");
            
        } catch (Exception e) {
            fail("Failed to validate hook methods: " + e.getMessage());
        }
    }
}
