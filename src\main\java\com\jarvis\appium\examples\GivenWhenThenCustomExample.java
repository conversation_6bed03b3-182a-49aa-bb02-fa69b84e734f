package com.jarvis.appium.examples;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.Arrays;
import java.util.List;

/**
 * Example showing how to create Given-When-Then style tests with custom features
 */
public class GivenWhenThenCustomExample {
    
    private static final Logger logger = LoggerFactory.getLogger(GivenWhenThenCustomExample.class);
    
    private static BufferedWriter writer;
    private static BufferedReader reader;
    private static BufferedReader errorReader;
    
    public static void main(String[] args) {
        try {
            executeGivenWhenThenScenarios();
        } catch (Exception e) {
            logger.error("Given-When-Then custom example failed", e);
        }
    }
    
    private static void executeGivenWhenThenScenarios() throws IOException, InterruptedException {
        logger.info("=== 📝 Given-When-Then Custom Scenarios ===");
        
        // Start MCP server
        String[] command = {"node", "dist/index.js"};
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.directory(new File("C:\\POC\\AI\\MCP Integration Server\\jarvis-appium"));
        processBuilder.redirectErrorStream(false);
        
        logger.info("🚀 Starting MCP server...");
        Process process = processBuilder.start();
        
        writer = new BufferedWriter(new OutputStreamWriter(process.getOutputStream()));
        reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
        
        // Start output readers
        startOutputReaders();
        Thread.sleep(3000);
        
        // Initialize MCP
        initializeMcp();
        
        // Execute scenarios
        executeScenario1_CustomSwipeGesture();
        executeScenario2_DeviceInformation();
        executeScenario3_WaitForElement();
        
        // Clean up
        logger.info("🧹 Cleaning up...");
        writer.close();
        reader.close();
        errorReader.close();
        process.destroy();
    }
    
    private static void initializeMcp() throws IOException, InterruptedException {
        logger.info("🔗 Initializing MCP connection...");
        sendMcpRequest("initialize", "{\"protocolVersion\":\"2024-11-05\",\"clientInfo\":{\"name\":\"gwt-custom-test\",\"version\":\"1.0.0\"},\"capabilities\":{}}");
        Thread.sleep(2000);
        
        logger.info("📱 Selecting Android platform...");
        sendMcpRequest("tools/call", "{\"name\":\"select_platform\",\"arguments\":{\"platform\":\"android\"}}");
        Thread.sleep(2000);
        
        logger.info("🎯 Creating Android session...");
        String sessionCapabilities = "{\"platform\":\"android\",\"capabilities\":{\"platformName\":\"Android\",\"deviceName\":\"emulator-5554\",\"automationName\":\"UiAutomator2\"}}";
        sendMcpRequest("tools/call", "{\"name\":\"create_session\",\"arguments\":" + sessionCapabilities + "}");
        Thread.sleep(10000);
    }
    
    /**
     * Scenario 1: Custom Swipe Gesture
     * Given I have an active mobile session
     * When I perform a custom swipe gesture from top to bottom
     * Then the screen should respond to the swipe
     */
    private static void executeScenario1_CustomSwipeGesture() throws IOException, InterruptedException {
        logger.info("=== 📱 Scenario 1: Custom Swipe Gesture ===");
        
        // Given: I have an active mobile session
        logger.info("✅ GIVEN: I have an active mobile session");
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(2000);
        
        // When: I perform a custom swipe gesture from top to bottom
        logger.info("🔄 WHEN: I perform a custom swipe gesture from top to bottom");
        String swipeParams = "{\"startX\": 500, \"startY\": 200, \"endX\": 500, \"endY\": 800, \"duration\": 1500}";
        sendMcpRequest("tools/call", "{\"name\":\"custom_swipe_gesture\",\"arguments\":" + swipeParams + "}");
        Thread.sleep(3000);
        
        // Then: The screen should respond to the swipe
        logger.info("✅ THEN: The screen should respond to the swipe");
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(2000);
        
        logger.info("✅ Scenario 1 completed successfully!");
    }
    
    /**
     * Scenario 2: Device Information Retrieval
     * Given I have an active mobile session
     * When I request comprehensive device information
     * Then I should receive detailed device data
     */
    private static void executeScenario2_DeviceInformation() throws IOException, InterruptedException {
        logger.info("=== 📊 Scenario 2: Device Information Retrieval ===");
        
        // Given: I have an active mobile session
        logger.info("✅ GIVEN: I have an active mobile session");
        
        // When: I request comprehensive device information
        logger.info("📊 WHEN: I request comprehensive device information");
        sendMcpRequest("tools/call", "{\"name\":\"custom_get_device_info\",\"arguments\":{}}");
        Thread.sleep(3000);
        
        // Then: I should receive detailed device data
        logger.info("✅ THEN: I should receive detailed device data");
        logger.info("Device information retrieved successfully!");
        
        logger.info("✅ Scenario 2 completed successfully!");
    }
    
    /**
     * Scenario 3: Wait for Element with Custom Timeout
     * Given I have an active mobile session
     * When I wait for a clickable element to appear with a 10-second timeout
     * Then the element should be found within the timeout period
     */
    private static void executeScenario3_WaitForElement() throws IOException, InterruptedException {
        logger.info("=== ⏰ Scenario 3: Wait for Element with Custom Timeout ===");
        
        // Given: I have an active mobile session
        logger.info("✅ GIVEN: I have an active mobile session");
        
        // When: I wait for a clickable element to appear with a 10-second timeout
        logger.info("⏰ WHEN: I wait for a clickable element to appear with a 10-second timeout");
        String waitParams = "{\"strategy\": \"xpath\", \"selector\": \"//*[@clickable='true'][1]\", \"timeout\": 10}";
        sendMcpRequest("tools/call", "{\"name\":\"custom_wait_for_element\",\"arguments\":" + waitParams + "}");
        Thread.sleep(5000);
        
        // Then: The element should be found within the timeout period
        logger.info("✅ THEN: The element should be found within the timeout period");
        logger.info("Element found successfully within timeout!");
        
        logger.info("✅ Scenario 3 completed successfully!");
    }
    
    private static void sendMcpRequest(String method, String params) throws IOException {
        String request = "{\"jsonrpc\":\"2.0\",\"id\":\"" + System.currentTimeMillis() + "\",\"method\":\"" + method + "\",\"params\":" + params + "}";
        logger.info("📤 SENDING: {}", request);
        writer.write(request);
        writer.newLine();
        writer.flush();
    }
    
    private static void startOutputReaders() {
        Thread outputThread = new Thread(() -> {
            try {
                String line;
                while ((line = reader.readLine()) != null) {
                    // Filter out base64 screenshot data and only show important messages
                    if (line.contains("session created") || line.contains("Custom")) {
                        logger.info("📥 SERVER: {}", line);
                    } else if (line.contains("screenshot") && line.contains("data:image")) {
                        logger.info("📸 Screenshot taken successfully");
                    } else if (line.contains("\"result\"") && !line.contains("data:image") && line.length() < 500) {
                        // Only log short result messages, avoid base64 data
                        logger.info("📥 SERVER: {}", line);
                    }
                }
            } catch (IOException e) {
                // Expected when process ends
            }
        });
        outputThread.setDaemon(true);
        outputThread.start();

        Thread errorThread = new Thread(() -> {
            try {
                String line;
                while ((line = errorReader.readLine()) != null) {
                    if (line.contains("ERROR") && !line.contains("DEBUG")) {
                        logger.error("⚠️ SERVER ERROR: {}", line);
                    }
                }
            } catch (IOException e) {
                // Expected when process ends
            }
        });
        errorThread.setDaemon(true);
        errorThread.start();
    }
}
