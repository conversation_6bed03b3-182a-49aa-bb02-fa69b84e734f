package com.jarvis.appium.examples;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.concurrent.TimeUnit;

/**
 * Debug MCP connection to see what's happening
 */
public class DebugMcpConnection {
    
    private static final Logger logger = LoggerFactory.getLogger(DebugMcpConnection.class);
    
    private static BufferedWriter writer;
    private static int requestId = 1;
    
    public static void main(String[] args) {
        try {
            testMcpConnection();
        } catch (Exception e) {
            logger.error("Debug test failed", e);
        }
    }
    
    private static void testMcpConnection() throws IOException, InterruptedException {
        logger.info("=== 🔍 Debug MCP Connection ===");
        
        // Start MCP server using node directly
        String[] command = {"node", "C:\\POC\\AI\\MCP Integration Server\\jarvis-appium\\dist\\index.js"};
        
        logger.info("🚀 Starting MCP server with command: {}", String.join(" ", command));
        
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.redirectErrorStream(false);
        
        Process process = processBuilder.start();
        
        writer = new BufferedWriter(new OutputStreamWriter(process.getOutputStream()));
        BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
        
        // Start output readers with detailed logging
        startDetailedOutputReaders(reader, errorReader);
        
        // Wait for server to start
        logger.info("⏳ Waiting 5 seconds for server to start...");
        Thread.sleep(5000);
        
        // Test 1: Initialize MCP
        logger.info("🔗 Test 1: Initializing MCP connection...");
        sendMcpRequest("initialize", "{\"protocolVersion\":\"2024-11-05\",\"clientInfo\":{\"name\":\"debug-client\",\"version\":\"1.0.0\"},\"capabilities\":{\"roots\":{\"listChanged\":true},\"tools\":{\"listChanged\":true}}}");
        Thread.sleep(3000);
        
        // Test 2: List available tools
        logger.info("🛠️ Test 2: Listing available tools...");
        sendMcpRequest("tools/list", "{}");
        Thread.sleep(3000);
        
        // Test 3: Check Android devices
        logger.info("📱 Test 3: Checking Android devices...");
        sendMcpRequest("tools/call", "{\"name\":\"select_platform\",\"arguments\":{\"platform\":\"android\"}}");
        Thread.sleep(3000);
        
        // Test 4: Try to create a simple session
        logger.info("🎯 Test 4: Creating basic Android session...");
        String basicCapabilities = "{\"platform\":\"android\",\"capabilities\":{\"platformName\":\"Android\",\"deviceName\":\"emulator-5554\",\"automationName\":\"UiAutomator2\"}}";
        sendMcpRequest("tools/call", "{\"name\":\"create_session\",\"arguments\":" + basicCapabilities + "}");
        Thread.sleep(10000);
        
        // Test 5: Take screenshot if session created
        logger.info("📸 Test 5: Attempting screenshot...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(3000);
        
        // Clean up
        logger.info("🧹 Cleaning up...");
        writer.close();
        reader.close();
        errorReader.close();
        
        process.destroy();
        if (!process.waitFor(5, TimeUnit.SECONDS)) {
            process.destroyForcibly();
        }
        
        logger.info("=== Debug Test Complete ===");
    }
    
    private static void sendMcpRequest(String method, String params) throws IOException {
        String request = "{\"jsonrpc\":\"2.0\",\"id\":\"" + (requestId++) + "\",\"method\":\"" + method + "\",\"params\":" + params + "}";
        logger.info("📤 Sending: {}", request);
        writer.write(request);
        writer.newLine();
        writer.flush();
    }
    
    private static void startDetailedOutputReaders(BufferedReader reader, BufferedReader errorReader) {
        // Thread to read stderr with detailed logging
        Thread errorThread = new Thread(() -> {
            try {
                String line;
                while ((line = errorReader.readLine()) != null) {
                    logger.error("🔴 STDERR: {}", line);
                }
            } catch (IOException e) {
                logger.debug("Error reading stderr: {}", e.getMessage());
            }
        });
        errorThread.setDaemon(true);
        errorThread.start();
        
        // Thread to read stdout with detailed logging
        Thread readerThread = new Thread(() -> {
            try {
                String line;
                while ((line = reader.readLine()) != null) {
                    if (line.trim().startsWith("{")) {
                        // Filter out base64 screenshot data
                        if (line.contains("screenshot") && line.contains("data:image")) {
                            logger.info("📸 Screenshot response received (base64 data filtered)");
                        } else if (line.length() < 1000) {
                            logger.info("📥 JSON Response: {}", line);
                        } else {
                            logger.info("📥 Large JSON Response received ({}+ chars)", line.length());
                        }
                    } else if (!line.trim().isEmpty()) {
                        logger.info("📝 Server Log: {}", line);
                    }
                }
            } catch (IOException e) {
                logger.debug("Error reading stdout: {}", e.getMessage());
            }
        });
        readerThread.setDaemon(true);
        readerThread.start();
    }
}
