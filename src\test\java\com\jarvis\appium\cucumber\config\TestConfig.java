package com.jarvis.appium.cucumber.config;

/**
 * Test Configuration for Cucumber Single Scenario Tests
 * 
 * Centralized configuration management for the Cucumber test execution
 */
public class TestConfig {
    
    // App Configuration
    public static final String APK_PATH = "C:\\POC\\AI\\MCP Integration Server\\mcp-client-integration\\Android.SauceLabs.Mobile.Sample.app.2.7.1.apk";
    public static final String PACKAGE_NAME = "com.swaglabsmobileapp";
    public static final String ACTIVITY_NAME = "com.swaglabsmobileapp.SplashActivity";
    
    // MCP Server Configuration
    public static final String MCP_SERVER_PATH = "C:\\POC\\AI\\MCP Integration Server\\jarvis-appium";
    public static final String[] MCP_COMMAND = {"node", "dist/index.js"};
    
    // Device Configuration
    public static final String DEVICE_NAME = "emulator-5554";
    public static final String PLATFORM_NAME = "Android";
    public static final String AUTOMATION_NAME = "UiAutomator2";
    
    // Timeouts (in milliseconds)
    public static final int APP_LAUNCH_TIMEOUT = 20000;
    public static final int MCP_INIT_TIMEOUT = 3000;
    public static final int ELEMENT_WAIT_TIMEOUT = 5000;
    public static final int SCREENSHOT_TIMEOUT = 3000;
    public static final int COMMAND_TIMEOUT = 300;
    
    // Test Settings
    public static final boolean NO_RESET = false;
    public static final boolean FULL_RESET = false;
    public static final boolean AUTO_GRANT_PERMISSIONS = true;
    
    // Reporting
    public static final String REPORTS_DIR = "target/cucumber-reports";
    public static final String SCREENSHOTS_DIR = "target/screenshots";
    
    /**
     * Get session capabilities as JSON string
     */
    public static String getSessionCapabilities() {
        return "{"
            + "\"platform\":\"android\","
            + "\"capabilities\":{"
            + "\"platformName\":\"" + PLATFORM_NAME + "\","
            + "\"deviceName\":\"" + DEVICE_NAME + "\","
            + "\"app\":\"" + APK_PATH.replace("\\", "\\\\") + "\","
            + "\"appPackage\":\"" + PACKAGE_NAME + "\","
            + "\"appActivity\":\"" + ACTIVITY_NAME + "\","
            + "\"automationName\":\"" + AUTOMATION_NAME + "\","
            + "\"newCommandTimeout\":" + COMMAND_TIMEOUT + ","
            + "\"autoGrantPermissions\":" + AUTO_GRANT_PERMISSIONS + ","
            + "\"noReset\":" + NO_RESET + ","
            + "\"fullReset\":" + FULL_RESET
            + "}"
            + "}";
    }
    
    /**
     * Get MCP initialization parameters
     */
    public static String getMcpInitParams() {
        return "{\"protocolVersion\":\"2024-11-05\","
            + "\"clientInfo\":{\"name\":\"cucumber-single-scenario\",\"version\":\"1.0.0\"},"
            + "\"capabilities\":{}}";
    }
}
