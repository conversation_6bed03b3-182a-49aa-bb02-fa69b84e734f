package com.jarvis.appium.cucumber.runners;

import io.cucumber.testng.AbstractTestNGCucumberTests;
import io.cucumber.testng.CucumberOptions;
import org.testng.annotations.DataProvider;

/**
 * TestNG-based Cucumber Runner for Single Scenario Tests
 * 
 * Alternative runner using TestNG instead of JUnit Platform
 * Based on the original SingleScenarioRunner functionality
 */
@CucumberOptions(
    features = "src/test/resources/features/SingleScenario.feature",
    glue = {"com.jarvis.appium.cucumber.steps"},
    tags = "@SingleScenario",
    plugin = {
        "pretty",
        "html:target/cucumber-reports/single-scenario",
        "json:target/cucumber-reports/single-scenario/Cucumber.json",
        "junit:target/cucumber-reports/single-scenario/Cucumber.xml"
    },
    monochrome = true,
    dryRun = false
)
public class SingleScenarioTestNGRunner extends AbstractTestNGCucumberTests {

    /**
     * Enable parallel execution of scenarios
     */
    @Override
    @DataProvider(parallel = false) // Set to true for parallel execution
    public Object[][] scenarios() {
        return super.scenarios();
    }
}
