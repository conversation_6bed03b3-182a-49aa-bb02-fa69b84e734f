@SingleScenario
Feature: Single Scenario Test for SauceLabs App
  As a tester
  I want to run a single scenario to verify the SauceLabs app functionality
  So that I can quickly test the basic product catalog features

  Background:
    Given the SauceLabs app is launched and ready

  @ProductCatalog @Smoke
  Scenario: View Product Catalog
    Given the SauceLabs app is launched
    When I view the product catalog
    Then I should see Sauce Labs Backpack
    And I should see Sauce Labs Bike Light
    And I should see the sort button

  @ProductDetails @Regression
  Scenario: View Product Details
    Given the SauceLabs app is launched
    When I view the product catalog
    And I click on "Sauce Labs Backpack"
    Then I should see the product details page
    And I should see the product name "Sauce Labs Backpack"
    And I should see the product price
    And I should see the Add To Cart button

  @Navigation @Smoke
  Scenario: Basic Navigation Test
    Given the SauceLabs app is launched
    When I view the product catalog
    And I generate locators for current screen
    Then I should see interactive elements
    And I should be able to take a screenshot

  @ElementDiscovery @Debug
  Scenario: Element Discovery Test
    Given the SauceLabs app is launched
    When I view the product catalog
    And I generate locators for current screen
    Then I should find the username field
    And I should find navigation elements
    And I should capture the current screen state
