# 🧪 Generate Code with Locators from Given-When-Then Scenarios

## 🎯 Overview

The `generate://code-with-locators` functionality demonstrates how to automatically generate complete test code with intelligent element locators using **Given-When-Then scenarios**. This feature combines BDD-style test scenarios, element discovery, locator optimization, and test code generation into a seamless workflow using the `appium_generate_tests` MCP tool.

## 🚀 Quick Start

```bash
# Run the complete code generation example
mvn exec:java -Dexec.mainClass="com.jarvis.appium.examples.SauceLabsCodeGenerationExample"
```

## 🔧 What It Does

### Phase 1: Element Discovery
- Launches the SauceLabs Demo App
- Automatically discovers all interactive elements
- Captures screenshots at key points
- Maps the app's UI structure

### Phase 2: Intelligent Locator Generation
- Creates optimized locators using priority strategy:
  1. **ID locators** (highest priority - most stable)
  2. **Accessibility ID** (good for cross-platform)
  3. **Class name** (platform-specific)
  4. **XPath** (fallback option)

### Phase 3: BDD Test Code Generation
- **Parses Given-When-Then scenarios** in Gherkin format
- **Generates complete Java test methods** from BDD scenarios
- **Maps scenario steps** to actual UI interactions
- **Includes error handling and assertions**
- **Adds screenshot capture points**
- **Creates reusable step definitions**

### Phase 4: Advanced BDD Scenarios
- **Login testing** with valid/invalid credentials
- **End-to-end shopping flows** with multiple products
- **Product sorting and filtering** scenarios
- **Data-driven test scenarios** with test data providers

### Phase 5: Multi-Format Export
- **JUnit format** - Ready-to-run JUnit test classes with BDD support
- **TestNG format** - TestNG-compatible test methods
- **Cucumber format** - Feature files and step definitions
- **Page Object Model** - Structured page objects with BDD integration
- **Step definitions** - Reusable Cucumber step implementations

## 📝 BDD Scenarios Used

The example uses these Given-When-Then scenarios to generate test code:

### 🛍️ Product Catalog Scenario
```gherkin
Scenario: Browse Product Catalog
Given the SauceLabs app is launched
When I view the product catalog
And I click on Sauce Labs Backpack
Then I should see product details
And I should see the product price
And I should see the Add To Cart button
```

### 🛒 Add to Cart Scenario
```gherkin
Scenario: Add Product to Cart
Given the SauceLabs app is launched
When I click on Sauce Labs Backpack
And I click the Add To Cart button
Then the cart badge should show 1 item
And I should see the cart icon updated
```

### 🔐 Login Scenarios
```gherkin
Scenario: Valid User Login
Given the SauceLabs app is launched
When I open the menu
And I navigate to login
And I enter username 'standard_user'
And I enter password 'secret_sauce'
And I click the login button
Then I should be logged in successfully
And I should see the products page

Scenario: Invalid User Login
Given the SauceLabs app is launched
When I open the menu
And I navigate to login
And I enter username 'invalid_user'
And I enter password 'wrong_password'
And I click the login button
Then I should see an error message
And I should remain on the login screen
```

### 🎯 End-to-End Shopping Scenario
```gherkin
Scenario: Complete Shopping Flow
Given the SauceLabs app is launched
When I click on Sauce Labs Backpack
And I click the Add To Cart button
And I click on Sauce Labs Bike Light
And I click the Add To Cart button
And I click on cart badge
Then I should see 2 items in cart
And I should see the correct total price
And I should be able to proceed to checkout
```

## 📱 Generated Test Code Coverage

The BDD scenarios generate test code for all major app features:

### 🛍️ Product Catalog BDD Test
```java
// Generated from "Browse Product Catalog" scenario
@Test
@Description("Scenario: Browse Product Catalog")
public void testBrowseProductCatalog() {
    // Given the SauceLabs app is launched
    launchSauceLabsApp();

    // When I view the product catalog
    viewProductCatalog();

    // And I click on Sauce Labs Backpack
    clickOnProduct("Sauce Labs Backpack");

    // Then I should see product details
    verifyProductDetailsDisplayed();

    // And I should see the product price
    verifyProductPriceDisplayed();

    // And I should see the Add To Cart button
    verifyAddToCartButtonDisplayed();
}

// Generated locators with fallback strategies
@FindBy(accessibility = "Sauce Labs Backpack")
@FindBy(xpath = "//android.widget.TextView[@text='Sauce Labs Backpack']")
private WebElement sauceLabsBackpack;

@FindBy(id = "com.saucelabs.mydemoapp.android:id/productPrice")
@FindBy(xpath = "//android.widget.TextView[@text='$29.99']")
private WebElement productPrice;
```

### 🛒 Shopping Cart BDD Test
```java
// Generated from "Add Product to Cart" scenario
@Test
@Description("Scenario: Add Product to Cart")
public void testAddProductToCart() {
    // Given the SauceLabs app is launched
    launchSauceLabsApp();

    // When I click on Sauce Labs Backpack
    clickOnProduct("Sauce Labs Backpack");

    // And I click the Add To Cart button
    clickAddToCartButton();

    // Then the cart badge should show 1 item
    verifyCartBadgeCount(1);

    // And I should see the cart icon updated
    verifyCartIconUpdated();
}

// Generated step methods
private void clickAddToCartButton() {
    WebElement addToCartBtn = findElementWithFallback(
        By.accessibilityId("Add To Cart button"),
        By.id("com.saucelabs.mydemoapp.android:id/cartBT"),
        By.xpath("//android.widget.Button[@text='ADD TO CART']")
    );
    addToCartBtn.click();
    takeScreenshot("add_to_cart_clicked");
}
```

### 🔐 Login BDD Tests
```java
// Generated from login scenarios with data provider
@Test(dataProvider = "loginScenarios")
@Description("Login test scenarios with valid and invalid credentials")
public void testLoginScenarios(String username, String password, String expectedResult) {
    // Given the SauceLabs app is launched
    launchSauceLabsApp();

    // When I open the menu
    openHamburgerMenu();

    // And I navigate to login
    navigateToLogin();

    // And I enter credentials
    enterUsername(username);
    enterPassword(password);

    // And I click the login button
    clickLoginButton();

    // Then verify result based on scenario
    switch (expectedResult) {
        case "success":
            verifySuccessfulLogin();
            verifyProductsPageDisplayed();
            break;
        case "error":
            verifyErrorMessageDisplayed();
            verifyRemainsOnLoginScreen();
            break;
    }
}

@DataProvider(name = "loginScenarios")
public Object[][] getLoginScenarios() {
    return new Object[][] {
        {"standard_user", "secret_sauce", "success"},
        {"invalid_user", "wrong_password", "error"},
        {"locked_out_user", "secret_sauce", "error"}
    };
}
```

## 🎨 Generated Code Features

### ✅ Robust Element Location
- **Multiple fallback strategies** for each element
- **Platform-specific optimizations** (Android UiAutomator2)
- **Wait conditions** for dynamic elements
- **Error handling** for missing elements

### ✅ Test Structure
- **Page Object Model** pattern implementation
- **Reusable helper methods** for common actions
- **Data-driven test support** with test data providers
- **Screenshot capture** at assertion points

### ✅ Maintenance-Friendly
- **Centralized locator management** in page objects
- **Configurable timeouts** and retry logic
- **Detailed logging** for debugging
- **Cross-platform compatibility** preparation

## 📊 Example Output

### Generated Test Class Structure
```java
public class SauceLabsGeneratedTests {
    
    // Page Objects
    private ProductCatalogPage catalogPage;
    private ProductDetailsPage detailsPage;
    private ShoppingCartPage cartPage;
    private LoginPage loginPage;
    
    // Test Methods
    @Test
    public void testProductCatalogFlow() { /* Generated implementation */ }
    
    @Test
    public void testShoppingCartOperations() { /* Generated implementation */ }
    
    @Test(dataProvider = "loginData")
    public void testLoginScenarios(String user, String pass) { /* Generated implementation */ }
}
```

### Generated Page Object Example
```java
public class ProductCatalogPage extends BasePage {
    
    // Locators with fallback strategies
    @FindBy(id = "com.saucelabs.mydemoapp.android:id/productIV")
    @FindBy(xpath = "//android.widget.ImageView[@content-desc='Sauce Labs Backpack']")
    private WebElement sauceLabsBackpack;
    
    // Generated interaction methods
    public ProductDetailsPage selectProduct(String productName) {
        WebElement product = findElementWithFallback(
            By.accessibilityId(productName),
            By.xpath("//android.widget.TextView[@text='" + productName + "']")
        );
        product.click();
        return new ProductDetailsPage(driver);
    }
}
```

## 🔧 Configuration Options

### Locator Strategy Priority
```java
// Customize locator generation priority
LocatorConfig config = new LocatorConfig()
    .setPrimaryStrategy(LocatorStrategy.ID)
    .setSecondaryStrategy(LocatorStrategy.ACCESSIBILITY_ID)
    .setFallbackStrategy(LocatorStrategy.XPATH)
    .setIncludeClassNames(true)
    .setOptimizeForStability(true);
```

### Code Generation Options
```java
// Customize generated code format
CodeGenerationConfig codeConfig = new CodeGenerationConfig()
    .setFramework(TestFramework.JUNIT)
    .setPageObjectPattern(true)
    .setDataDrivenTests(true)
    .setIncludeScreenshots(true)
    .setErrorHandling(ErrorHandlingLevel.COMPREHENSIVE);
```

## 📁 BDD Output Structure

```
generated-tests/
├── SauceLabsBDDTests.java        # Main BDD test class (JUnit)
├── SauceLabsBDDTestNG.java       # BDD test class (TestNG)
├── features/
│   └── SauceLabs.feature         # Cucumber feature file
├── steps/
│   └── SauceLabsSteps.java       # Cucumber step definitions
├── pages/
│   ├── ProductCatalogPage.java   # Product catalog page object with BDD support
│   ├── ProductDetailsPage.java   # Product details page object
│   ├── ShoppingCartPage.java     # Shopping cart page object
│   └── LoginPage.java            # Login page object
├── data/
│   ├── TestData.java             # BDD test data providers
│   ├── LoginCredentials.csv      # CSV data for login scenarios
│   └── ProductData.csv           # Product test data
└── utils/
    ├── BasePage.java             # Base page object class with BDD utilities
    ├── BDDTestUtils.java         # BDD-specific test utilities
    └── ScenarioContext.java      # Context sharing between steps
```

### 🥒 Generated Cucumber Feature File
```gherkin
Feature: SauceLabs Demo App Testing
  As a user of the SauceLabs demo app
  I want to browse products, add them to cart, and login
  So that I can complete my shopping experience

  Scenario: Browse Product Catalog
    Given the SauceLabs app is launched
    When I view the product catalog
    And I click on Sauce Labs Backpack
    Then I should see product details
    And I should see the product price
    And I should see the Add To Cart button

  Scenario Outline: User Login
    Given the SauceLabs app is launched
    When I open the menu
    And I navigate to login
    And I enter username '<username>'
    And I enter password '<password>'
    And I click the login button
    Then I should see '<result>'

    Examples:
      | username      | password     | result           |
      | standard_user | secret_sauce | successful login |
      | invalid_user  | wrong_pass   | error message    |
```

## 🎯 Benefits

### 🚀 Speed
- **Instant test creation** from app exploration
- **No manual locator hunting** required
- **Ready-to-run code** generation

### 🛡️ Reliability
- **Multiple locator strategies** for resilience
- **Optimized selectors** for stability
- **Built-in error handling** and retries

### 🔧 Maintainability
- **Page Object Model** structure
- **Centralized locator management**
- **Consistent coding patterns**

### 📈 Scalability
- **Template-based generation** for consistency
- **Easy extension** for new features
- **Cross-platform preparation**

## 🚨 Prerequisites

1. **SauceLabs Demo App** installed at configured path
2. **Android device/emulator** connected and ready
3. **MCP server** running (`npx jarvis-appium`)
4. **Java 11+** and Maven configured

## 🎮 Try It Now

1. **Update the APK path** in the example file
2. **Ensure device is connected**
3. **Run the example**:
   ```bash
   mvn exec:java -Dexec.mainClass="com.jarvis.appium.examples.SauceLabsCodeGenerationExample"
   ```
4. **Check the generated code** in `generated-tests/` directory
5. **Run the generated tests** to verify functionality

## 🔍 What You'll See

- **Real-time element discovery** as the app is explored
- **Intelligent locator generation** with priority-based selection
- **Complete test code creation** with proper structure
- **Multiple output formats** for different testing needs
- **Ready-to-use test classes** that you can immediately run

This example showcases the power of AI-assisted test automation, turning manual test creation into an automated, intelligent process that produces maintainable, robust test code.
