# Jarvis Appium Java Client

A comprehensive Java client for the Jarvis Appium MCP (Model Context Protocol) server, providing intelligent mobile automation capabilities with AI-powered test generation and element locator optimization.

## Features

- **MCP Protocol Integration**: Full support for JSON-RPC communication with the Jarvis Appium MCP server
- **Cross-Platform Support**: Android (UiAutomator2) and iOS (XCUITest) automation
- **Intelligent Element Location**: AI-powered locator generation with priority-based selection
- **LambdaTest Cloud Integration**: Seamless cloud testing capabilities
- **Test Generation Framework**: AI-assisted test scenario creation and execution
- **Configuration Management**: Flexible configuration with multiple loading strategies
- **Comprehensive Examples**: Ready-to-use examples for common automation scenarios

## Quick Start

### Prerequisites

- Java 11 or higher
- Maven 3.6+
- Node.js (for the MCP server)
- Jarvis Appium MCP server installed (`npm install -g jarvis-appium`)

### Installation

1. Clone this repository
2. Build the project:
```bash
mvn clean compile
```

### Basic Usage

```java
import com.jarvis.appium.client.JarvisAppiumClient;
import com.jarvis.appium.config.ConfigManager;
import com.jarvis.appium.model.Platform;

// Load configuration and connect
JarvisAppiumConfig config = ConfigManager.loadConfig();
try (JarvisAppiumClient client = new JarvisAppiumClient()) {
    client.connect(config.getServer().getCommand());
    
    // Select platform and create session
    client.selectPlatform(Platform.ANDROID);
    SessionInfo session = client.createSession(Platform.ANDROID);
    
    // Generate intelligent locators
    List<ElementLocator> locators = client.generateLocators();
    
    // Find and interact with elements
    String elementId = client.findElement(LocatorStrategy.ID, "com.example:id/button");
    client.clickElement(elementId);
}
```

## Configuration

Create a `jarvis-appium.json` configuration file:

```json
{
  "server": {
    "command": ["npx", "jarvis-appium"],
    "timeout": 30,
    "environment": {}
  },
  "capabilities": {
    "android": {
      "appium:automationName": "UiAutomator2",
      "appium:deviceName": "Android Device",
      "appium:platformName": "Android"
    },
    "ios": {
      "appium:automationName": "XCUITest",
      "appium:deviceName": "iPhone Simulator",
      "appium:platformName": "iOS"
    }
  },
  "lambdatest": {
    "username": "your_username",
    "accessKey": "your_access_key",
    "gridUrl": "https://mobile-hub.lambdatest.com/wd/hub"
  },
  "test": {
    "timeout": 30000,
    "retryCount": 0,
    "screenshotOnFailure": true,
    "outputDirectory": "target/test-results"
  }
}
```

## Core Components

### JarvisAppiumClient

The main client class providing high-level automation methods:

- **Session Management**: `createSession()`, `createLambdaTestSession()`
- **Element Interaction**: `findElement()`, `clickElement()`, `setElementValue()`
- **Locator Generation**: `generateLocators()` with AI-powered optimization
- **Test Generation**: `generateTest()` for AI-assisted test creation
- **Utilities**: `takeScreenshot()`, `uploadAppToLambdaTest()`

### Test Framework

Create and execute test scenarios:

```java
TestScenario scenario = new TestScenario("Login Test", "Test login flow", Platform.ANDROID)
    .addStep("Click username field", TestStep.TestAction.CLICK, "username field")
    .addStep("Enter username", TestStep.TestAction.SET_TEXT, "username field", "<EMAIL>")
    .addStep("Click login button", TestStep.TestAction.CLICK, "login button");

TestExecutor executor = new TestExecutor(client);
TestResult result = executor.executeScenario(scenario);
```

### Element Locators

Intelligent element location with priority-based strategy selection:

```java
// Generate locators for current screen
List<ElementLocator> locators = client.generateLocators();

// Get best locator for platform
ElementLocator.LocatorInfo bestLocator = locator.getBestLocator(Platform.ANDROID);
String elementId = client.findElement(bestLocator.getStrategy(), bestLocator.getValue());
```

## Examples

### Basic Usage
```bash
mvn exec:java -Dexec.mainClass="com.jarvis.appium.examples.BasicUsageExample"
```

### LambdaTest Cloud Testing
```bash
mvn exec:java -Dexec.mainClass="com.jarvis.appium.examples.LambdaTestExample"
```

### Test Generation and Execution
```bash
mvn exec:java -Dexec.mainClass="com.jarvis.appium.examples.TestGenerationExample"
```

### SauceLabs App Testing with Code Generation
```bash
mvn exec:java -Dexec.mainClass="com.jarvis.appium.examples.SauceLabsCodeGenerationExample"
```

## Architecture

The client follows a layered architecture:

1. **MCP Layer**: Low-level JSON-RPC communication (`McpClient`)
2. **Appium Layer**: High-level automation wrapper (`JarvisAppiumClient`)
3. **Test Layer**: Test scenario creation and execution (`TestExecutor`)
4. **Configuration Layer**: Flexible configuration management (`ConfigManager`)

## Advanced Features

### AI-Powered Locator Generation

The client automatically generates optimized element locators using multiple strategies:
- ID-based locators (highest priority)
- Accessibility ID locators
- Platform-specific locators (UiAutomator for Android, Predicate/Class Chain for iOS)
- XPath locators (fallback)

### LambdaTest Integration

Seamless cloud testing with automatic app upload and session management:

```java
LambdaTestSessionConfig config = new LambdaTestSessionConfig(Platform.ANDROID, "Galaxy S21", "11.0")
    .withApp("lt://APP123456789")
    .withBuildName("Test Build")
    .withTestName("My Test");

SessionInfo session = client.createLambdaTestSession(config);
```

### Test Generation

AI-assisted test creation from natural language descriptions:

```java
String testDescription = "Test the login flow with valid credentials";
String generatedTest = client.generateTest(testDescription);
```

### Code Generation with Locators

Generate complete test code with intelligent locators for Sauce Labs app testing:

```java
// Example: generate://code-with-locators for SauceLabs Demo App
try (JarvisAppiumClient client = new JarvisAppiumClient()) {
    client.connect(config.getServer().getCommand());
    client.selectPlatform(Platform.ANDROID);

    // Create session with SauceLabs app
    SessionInfo session = client.createSession(Platform.ANDROID);

    // Generate locators for current screen
    List<ElementLocator> locators = client.generateLocators();

    // Generate test code with discovered locators
    List<String> testSteps = Arrays.asList(
        "Launch SauceLabs Demo App",
        "Verify product catalog is displayed",
        "Click on Sauce Labs Backpack product",
        "Add product to cart",
        "Verify cart badge updates",
        "Navigate to login screen",
        "Test login functionality"
    );

    String generatedCode = client.generateTests(testSteps);
    System.out.println("Generated test code with locators:");
    System.out.println(generatedCode);
}
```

This generates complete Java test code including:
- Optimized element locators (ID > Accessibility ID > XPath)
- Platform-specific selectors
- Error handling and assertions
- Screenshot capture points
- Reusable test methods

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Documentation

### Specialized Guides
- **[Code Generation with Locators](GENERATE_CODE_WITH_LOCATORS.md)** - Complete guide to automated test code generation
- **[SauceLabs APK Configuration](SAUCELABS_APK_CONFIG.md)** - SauceLabs demo app setup and testing
- **[Given-When-Then Guide](GIVEN_WHEN_THEN_GUIDE.md)** - BDD-style test automation
- **[APK Testing Guide](APK_TESTING_GUIDE.md)** - Comprehensive APK testing workflows

## Support

For issues and questions:
- Check the examples in `src/main/java/com/jarvis/appium/examples/`
- Review the specialized documentation guides above
- Review the Javadoc documentation
- Open an issue on GitHub

## Changelog

### Version 1.0.0
- Initial release with full MCP protocol support
- Android and iOS automation capabilities
- LambdaTest cloud integration
- AI-powered test generation
- Comprehensive configuration management
