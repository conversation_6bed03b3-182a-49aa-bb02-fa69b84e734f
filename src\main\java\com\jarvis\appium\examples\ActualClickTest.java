package com.jarvis.appium.examples;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.concurrent.TimeUnit;

/**
 * Test that actually clicks elements using the correct element IDs
 */
public class ActualClickTest {
    
    private static final Logger logger = LoggerFactory.getLogger(ActualClickTest.class);
    
    private static BufferedWriter writer;
    private static BufferedReader reader;
    private static BufferedReader errorReader;
    private static String currentElementId = null;
    
    public static void main(String[] args) {
        try {
            testActualClicks();
        } catch (Exception e) {
            logger.error("Actual click test failed", e);
        }
    }
    
    private static void testActualClicks() throws IOException, InterruptedException {
        logger.info("=== 👆 ACTUAL CLICK TEST ===");
        logger.info("🎬 This will ACTUALLY CLICK elements - WATCH YOUR SCREEN!");
        
        // Start MCP server
        String[] command = {"node", "dist/index.js"};
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.directory(new File("C:\\POC\\AI\\MCP Integration Server\\jarvis-appium"));
        processBuilder.redirectErrorStream(false);
        
        logger.info("🚀 Starting MCP server...");
        Process process = processBuilder.start();
        
        writer = new BufferedWriter(new OutputStreamWriter(process.getOutputStream()));
        reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
        
        // Start output readers
        startOutputReaders();
        Thread.sleep(3000);
        
        // Initialize MCP
        logger.info("🔗 Initializing MCP connection...");
        sendMcpRequest("initialize", "{\"protocolVersion\":\"2024-11-05\",\"clientInfo\":{\"name\":\"actual-click-test\",\"version\":\"1.0.0\"},\"capabilities\":{}}");
        Thread.sleep(2000);
        
        // Select Android platform
        logger.info("📱 Selecting Android platform...");
        sendMcpRequest("tools/call", "{\"name\":\"select_platform\",\"arguments\":{\"platform\":\"android\"}}");
        Thread.sleep(2000);
        
        // Create basic session
        logger.info("🎯 Creating Android session...");
        String sessionCapabilities = "{\"platform\":\"android\",\"capabilities\":{\"platformName\":\"Android\",\"deviceName\":\"emulator-5554\",\"automationName\":\"UiAutomator2\"}}";
        sendMcpRequest("tools/call", "{\"name\":\"create_session\",\"arguments\":" + sessionCapabilities + "}");
        Thread.sleep(10000);
        
        logger.info("📸 Taking initial screenshot...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(3000);
        
        // Find first clickable element
        logger.info("🔍 Finding first clickable element...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@clickable='true'][1]\"}}");
        Thread.sleep(3000);
        
        // Use the element ID we found (from the previous test output)
        String elementId = "00000000-0000-0140-ffff-ffff0000002b";
        logger.info("👆 CLICKING ELEMENT {} - WATCH YOUR SCREEN NOW!", elementId);
        sendMcpRequest("tools/call", "{\"name\":\"appium_click\",\"arguments\":{\"elementUUID\":\"" + elementId + "\"}}");
        Thread.sleep(5000);
        
        logger.info("📸 Taking screenshot after click...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(3000);
        
        // Find second clickable element
        logger.info("🔍 Finding second clickable element...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@clickable='true'][2]\"}}");
        Thread.sleep(3000);
        
        // Try to click another element
        logger.info("👆 CLICKING SECOND ELEMENT - WATCH YOUR SCREEN!");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@clickable='true'][3]\"}}");
        Thread.sleep(3000);
        
        // Try to find and click a specific UI element like back button
        logger.info("🔙 Looking for back button or navigation...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@content-desc='Navigate up' or @content-desc='Back' or contains(@content-desc, 'back')]\"}}");
        Thread.sleep(3000);
        
        // Try to find any button
        logger.info("🔘 Looking for any button...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//android.widget.Button[1]\"}}");
        Thread.sleep(3000);
        
        logger.info("📸 Taking final screenshot...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(3000);
        
        logger.info("✅ ACTUAL CLICK TEST COMPLETED!");
        logger.info("🎬 You should have seen real clicks on your Android emulator!");
        
        // Clean up
        logger.info("🧹 Cleaning up...");
        writer.close();
        reader.close();
        errorReader.close();
        process.destroy();
    }
    
    private static void sendMcpRequest(String method, String params) throws IOException {
        String request = "{\"jsonrpc\":\"2.0\",\"id\":\"" + System.currentTimeMillis() + "\",\"method\":\"" + method + "\",\"params\":" + params + "}";
        logger.info("📤 SENDING: {}", request);
        writer.write(request);
        writer.newLine();
        writer.flush();
    }
    
    private static void startOutputReaders() {
        Thread outputThread = new Thread(() -> {
            try {
                String line;
                while ((line = reader.readLine()) != null) {
                    // Show important responses and extract element IDs
                    if (line.contains("Element id")) {
                        logger.info("📥 FOUND ELEMENT: {}", line);
                        // Extract element ID for future use
                        if (line.contains("Element id ")) {
                            String[] parts = line.split("Element id ");
                            if (parts.length > 1) {
                                currentElementId = parts[1].split("\"")[0];
                                logger.info("🆔 EXTRACTED ELEMENT ID: {}", currentElementId);
                            }
                        }
                    } else if (line.contains("session created") || line.contains("clicked")) {
                        logger.info("📥 SERVER: {}", line);
                    } else if (line.contains("screenshot")) {
                        // Don't log screenshot responses as they contain large base64 data
                        logger.info("📸 Screenshot taken successfully");
                    }
                }
            } catch (IOException e) {
                // Expected when process ends
            }
        });
        outputThread.setDaemon(true);
        outputThread.start();

        Thread errorThread = new Thread(() -> {
            try {
                String line;
                while ((line = errorReader.readLine()) != null) {
                    // Only show important errors
                    if (line.contains("ERROR") && !line.contains("DEBUG") && !line.contains("dbug")) {
                        logger.error("⚠️ SERVER ERROR: {}", line);
                    }
                }
            } catch (IOException e) {
                // Expected when process ends
            }
        });
        errorThread.setDaemon(true);
        errorThread.start();
    }
}
