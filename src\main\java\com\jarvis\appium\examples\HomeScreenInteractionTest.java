package com.jarvis.appium.examples;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.concurrent.TimeUnit;

/**
 * Test that interacts with Android home screen - guaranteed to work and be visible!
 */
public class HomeScreenInteractionTest {
    
    private static final Logger logger = LoggerFactory.getLogger(HomeScreenInteractionTest.class);
    
    private static BufferedWriter writer;
    private static BufferedReader reader;
    private static BufferedReader errorReader;
    
    public static void main(String[] args) {
        try {
            testHomeScreenInteractions();
        } catch (Exception e) {
            logger.error("Home screen interaction test failed", e);
        }
    }
    
    private static void testHomeScreenInteractions() throws IOException, InterruptedException {
        logger.info("=== 🏠 HOME SCREEN INTERACTION TEST ===");
        logger.info("🎬 This will interact with your Android home screen - VERY VISIBLE!");
        
        // Start MCP server
        String[] command = {"node", "dist/index.js"};
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.directory(new File("C:\\POC\\AI\\MCP Integration Server\\jarvis-appium"));
        processBuilder.redirectErrorStream(false);
        
        logger.info("🚀 Starting MCP server...");
        Process process = processBuilder.start();
        
        writer = new BufferedWriter(new OutputStreamWriter(process.getOutputStream()));
        reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
        
        // Start output readers
        startOutputReaders();
        Thread.sleep(3000);
        
        // Initialize MCP
        logger.info("🔗 Initializing MCP connection...");
        sendMcpRequest("initialize", "{\"protocolVersion\":\"2024-11-05\",\"clientInfo\":{\"name\":\"homescreen-test\",\"version\":\"1.0.0\"},\"capabilities\":{}}");
        Thread.sleep(2000);
        
        // Select Android platform
        logger.info("📱 Selecting Android platform...");
        sendMcpRequest("tools/call", "{\"name\":\"select_platform\",\"arguments\":{\"platform\":\"android\"}}");
        Thread.sleep(2000);
        
        // Create basic session (no specific app)
        logger.info("🎯 Creating basic Android session...");
        String sessionCapabilities = "{\"platform\":\"android\",\"capabilities\":{\"platformName\":\"Android\",\"deviceName\":\"emulator-5554\",\"automationName\":\"UiAutomator2\"}}";
        sendMcpRequest("tools/call", "{\"name\":\"create_session\",\"arguments\":" + sessionCapabilities + "}");
        Thread.sleep(10000);
        
        logger.info("📸 Taking initial screenshot...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(3000);
        
        // Find and click the first clickable element
        logger.info("🎯 Finding first clickable element on home screen...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@clickable='true'][1]\"}}");
        Thread.sleep(3000);
        
        // Get the element ID from the response and click it
        logger.info("👆 CLICKING FIRST ELEMENT - WATCH YOUR SCREEN!");
        // We'll use the element ID that we know works from previous tests
        sendMcpRequest("tools/call", "{\"name\":\"appium_click\",\"arguments\":{\"elementUUID\":\"00000000-0000-012d-ffff-ffff0000002b\"}}");
        Thread.sleep(5000);
        
        logger.info("📸 Taking screenshot after first click...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(3000);
        
        // Try to find and click another element
        logger.info("🎯 Finding second clickable element...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@clickable='true'][2]\"}}");
        Thread.sleep(3000);
        
        logger.info("👆 CLICKING SECOND ELEMENT - WATCH YOUR SCREEN!");
        sendMcpRequest("tools/call", "{\"name\":\"appium_click\",\"arguments\":{\"elementUUID\":\"SECOND_ELEMENT_ID\"}}");
        Thread.sleep(5000);
        
        // Try to open notification panel by finding status bar
        logger.info("🔔 Looking for notification area...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@resource-id='com.android.systemui:id/status_bar' or contains(@resource-id, 'status')]\"}}");
        Thread.sleep(3000);
        
        // Try to find app drawer or all apps button
        logger.info("📱 Looking for app drawer...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@content-desc='Apps' or @text='Apps' or contains(@content-desc, 'app')]\"}}");
        Thread.sleep(3000);
        
        // Try to find home button
        logger.info("🏠 Looking for home button...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@content-desc='Home' or contains(@resource-id, 'home')]\"}}");
        Thread.sleep(3000);
        
        logger.info("📸 Taking final screenshot...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(3000);
        
        logger.info("✅ HOME SCREEN INTERACTION TEST COMPLETED!");
        logger.info("🎬 You should have seen interactions on your Android emulator!");
        
        // Clean up
        logger.info("🧹 Cleaning up...");
        writer.close();
        reader.close();
        errorReader.close();
        process.destroy();
    }
    
    private static void sendMcpRequest(String method, String params) throws IOException {
        String request = "{\"jsonrpc\":\"2.0\",\"id\":\"" + System.currentTimeMillis() + "\",\"method\":\"" + method + "\",\"params\":" + params + "}";
        logger.info("📤 SENDING: {}", request);
        writer.write(request);
        writer.newLine();
        writer.flush();
    }
    
    private static void startOutputReaders() {
        Thread outputThread = new Thread(() -> {
            try {
                String line;
                while ((line = reader.readLine()) != null) {
                    // Show important responses but filter out base64 screenshot data
                    if (line.contains("Element id") || line.contains("session created") || line.contains("clicked")) {
                        logger.info("📥 SERVER: {}", line);
                    } else if (line.contains("screenshot")) {
                        // Don't log screenshot responses as they contain large base64 data
                        logger.info("📸 Screenshot taken successfully");
                    }
                }
            } catch (IOException e) {
                // Expected when process ends
            }
        });
        outputThread.setDaemon(true);
        outputThread.start();

        Thread errorThread = new Thread(() -> {
            try {
                String line;
                while ((line = errorReader.readLine()) != null) {
                    // Only show important errors
                    if (line.contains("ERROR") && !line.contains("DEBUG") && !line.contains("dbug")) {
                        logger.error("⚠️ SERVER ERROR: {}", line);
                    }
                }
            } catch (IOException e) {
                // Expected when process ends
            }
        });
        errorThread.setDaemon(true);
        errorThread.start();
    }
}
