{"codeGeneration": {"framework": "junit", "outputDirectory": "generated-tests", "packageName": "com.saucelabs.generated.tests", "includePageObjects": true, "includeDataDriven": true, "includeScreenshots": true, "locatorStrategy": {"primary": "id", "secondary": "accessibility_id", "fallback": "xpath", "includeClassNames": true, "optimizeForStability": true}, "testStructure": {"usePageObjectModel": true, "generateBasePage": true, "includeTestUtils": true, "addErrorHandling": true, "includeWaitConditions": true}, "sauceLabsApp": {"packageName": "com.saucelabs.mydemoapp.android", "mainActivity": ".view.activities.SplashActivity", "testScenarios": [{"name": "ProductCatalogTest", "description": "Test product catalog functionality", "steps": ["Launch SauceLabs Demo App", "Verify product catalog is displayed", "Verify product items are visible", "Click on Sauce Labs Backpack product", "Verify product details page loads", "Add product to cart", "Verify cart badge updates"]}, {"name": "ShoppingCartTest", "description": "Test shopping cart operations", "steps": ["Navigate to shopping cart", "Verify product is in cart", "Test quantity adjustment", "Test remove from cart", "Verify cart updates correctly"]}, {"name": "LoginTest", "description": "Test login functionality", "dataProvider": "loginCredentials", "steps": ["Open hamburger menu", "Navigate to login screen", "Enter test credentials", "Verify login result", "Test logout if successful"]}, {"name": "EndToEndTest", "description": "Complete shopping flow test", "steps": ["Launch app and verify catalog", "Select and add multiple products", "Navigate to cart and verify", "Proceed to login", "Complete shopping flow"]}], "testData": {"loginCredentials": [{"username": "standard_user", "password": "secret_sauce", "expectedResult": "success"}, {"username": "locked_out_user", "password": "secret_sauce", "expectedResult": "locked_out"}, {"username": "invalid_user", "password": "wrong_password", "expectedResult": "invalid_credentials"}], "products": [{"name": "Sauce Labs Backpack", "price": "$29.99", "accessibilityId": "Sauce Labs Backpack"}, {"name": "Sauce Labs Bike Light", "price": "$9.99", "accessibilityId": "Sauce Labs Bike Light"}, {"name": "Sauce Labs Bolt T-Shirt", "price": "$15.99", "accessibilityId": "Sauce Labs Bolt T-Shirt"}]}}}, "mcpServer": {"command": ["node", "C:\\POC\\AI\\MCP Integration Server\\jarvis-appium\\dist\\index.js"], "timeout": 30, "retryAttempts": 3}, "appium": {"capabilities": {"platformName": "Android", "automationName": "UiAutomator2", "newCommandTimeout": 300, "autoGrantPermissions": true, "noReset": false}, "timeouts": {"implicit": 10, "pageLoad": 30, "script": 30}}, "export": {"formats": ["junit", "testng", "pageobject"], "includeComments": true, "generateReadme": true, "createTestSuite": true}}