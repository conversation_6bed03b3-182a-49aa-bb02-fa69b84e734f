package com.jarvis.appium.examples;

import com.jarvis.appium.client.JarvisAppiumClient;
import com.jarvis.appium.config.ConfigManager;
import com.jarvis.appium.config.JarvisAppiumConfig;
import com.jarvis.appium.model.ElementLocator;
import com.jarvis.appium.model.Platform;
import com.jarvis.appium.model.SessionInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * SauceLabs Code Generation Example
 * 
 * Demonstrates the generate://code-with-locators functionality for SauceLabs Demo App.
 * This example shows how to:
 * 1. Connect to the app and discover elements
 * 2. Generate intelligent locators
 * 3. Create reusable test code with optimized selectors
 * 4. Export generated code for use in test frameworks
 */
public class SauceLabsCodeGenerationExample {
    
    private static final Logger logger = LoggerFactory.getLogger(SauceLabsCodeGenerationExample.class);
    
    // SauceLabs Sample App Configuration
    private static final String APK_PATH = "C:\\POC\\AI\\MCP Integration Server\\mcp-client-integration\\Android.SauceLabs.Mobile.Sample.app.2.7.1.apk";
    private static final String PACKAGE_NAME = "com.saucelabs.mydemoapp.android";
    private static final String ACTIVITY_NAME = ".view.activities.SplashActivity";
    
    private static BufferedWriter writer;
    private static int requestId = 1;
    
    public static void main(String[] args) {
        try {
            demonstrateCodeGenerationWithLocators();
        } catch (Exception e) {
            logger.error("SauceLabs code generation example failed", e);
        }
    }
    
    /**
     * Main demonstration of generate://code-with-locators functionality
     */
    private static void demonstrateCodeGenerationWithLocators() throws IOException, InterruptedException {
        logger.info("=== 🧪 SauceLabs Code Generation with Locators Example ===");
        logger.info("📱 App: SauceLabs Demo App");
        logger.info("🎯 Goal: Generate complete test code with intelligent locators");
        
        String[] command = {"node", "C:\\POC\\AI\\MCP Integration Server\\jarvis-appium\\dist\\index.js"};
        
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.redirectErrorStream(false);
        
        Process process = processBuilder.start();
        
        writer = new BufferedWriter(new OutputStreamWriter(process.getOutputStream()));
        BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
        
        // Start threads to read output
        startOutputReaders(reader, errorReader);
        Thread.sleep(3000);
        
        try {
            // Phase 1: Initialize and setup
            initializeAndSetup();
            
            // Phase 2: Discover elements and generate locators
            discoverElementsAndGenerateLocators();
            
            // Phase 3: Generate test code with locators
            generateTestCodeWithLocators();
            
            // Phase 4: Generate advanced test scenarios
            generateAdvancedTestScenarios();
            
            // Phase 5: Export generated code
            exportGeneratedCode();
            
        } finally {
            // Cleanup
            logger.info("🧹 Cleaning up...");
            writer.close();
            reader.close();
            errorReader.close();
            process.destroy();
            if (!process.waitFor(5, TimeUnit.SECONDS)) {
                process.destroyForcibly();
            }
        }
        
        logger.info("✅ Code generation example completed successfully!");
    }
    
    /**
     * Phase 1: Initialize MCP connection and create session
     */
    private static void initializeAndSetup() throws IOException, InterruptedException {
        logger.info("\n🔧 Phase 1: Initialize and Setup");
        
        // Initialize MCP
        logger.info("🔗 Initializing MCP connection...");
        sendMcpRequest("initialize", "{\"protocolVersion\":\"2024-11-05\",\"clientInfo\":{\"name\":\"saucelabs-codegen\",\"version\":\"1.0.0\"},\"capabilities\":{\"roots\":{\"listChanged\":true},\"tools\":{\"listChanged\":true}}}");
        Thread.sleep(2000);
        
        // Select platform
        logger.info("📱 Selecting Android platform...");
        sendMcpRequest("tools/call", "{\"name\":\"select_platform\",\"arguments\":{\"platform\":\"android\"}}");
        Thread.sleep(2000);
        
        // Create session with SauceLabs app
        logger.info("🚀 Creating session with SauceLabs Demo App...");
        String sessionCapabilities = String.format(
            "{\"platform\":\"android\",\"capabilities\":{" +
            "\"platformName\":\"Android\"," +
            "\"app\":\"" + APK_PATH.replace("\\", "\\\\") + "\"," +
            "\"appPackage\":\"" + PACKAGE_NAME + "\"," +
            "\"appActivity\":\"" + ACTIVITY_NAME + "\"," +
            "\"automationName\":\"UiAutomator2\"," +
            "\"newCommandTimeout\":300," +
            "\"autoGrantPermissions\":true," +
            "\"noReset\":false" +
            "}}"
        );
        sendMcpRequest("tools/call", "{\"name\":\"create_session\",\"arguments\":" + sessionCapabilities + "}");
        Thread.sleep(8000); // Wait for app to launch
        
        // Take initial screenshot
        logger.info("📸 Taking initial screenshot...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(2000);
    }
    
    /**
     * Phase 2: Discover elements and generate intelligent locators
     */
    private static void discoverElementsAndGenerateLocators() throws IOException, InterruptedException {
        logger.info("\n🔍 Phase 2: Element Discovery and Locator Generation");
        
        // Generate locators for product catalog screen
        logger.info("🎯 Generating locators for product catalog screen...");
        sendMcpRequest("tools/call", "{\"name\":\"generate_locators\",\"arguments\":{}}");
        Thread.sleep(3000);
        
        // Navigate to product details to discover more elements
        logger.info("🛍️ Navigating to product details for more element discovery...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"accessibility id\",\"selector\":\"Sauce Labs Backpack\"}}");
        Thread.sleep(2000);
        
        // Click on first product
        sendMcpRequest("tools/call", "{\"name\":\"appium_click\",\"arguments\":{\"strategy\":\"accessibility id\",\"selector\":\"Sauce Labs Backpack\"}}");
        Thread.sleep(3000);
        
        // Generate locators for product details screen
        logger.info("📋 Generating locators for product details screen...");
        sendMcpRequest("tools/call", "{\"name\":\"generate_locators\",\"arguments\":{}}");
        Thread.sleep(3000);
        
        // Take screenshot of product details
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(2000);
        
        // Navigate to cart to discover cart elements
        logger.info("🛒 Adding to cart and discovering cart elements...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_click\",\"arguments\":{\"strategy\":\"accessibility id\",\"selector\":\"Add To Cart button\"}}");
        Thread.sleep(2000);
        
        // Click cart icon
        sendMcpRequest("tools/call", "{\"name\":\"appium_click\",\"arguments\":{\"strategy\":\"accessibility id\",\"selector\":\"cart badge\"}}");
        Thread.sleep(3000);
        
        // Generate locators for cart screen
        logger.info("🛒 Generating locators for shopping cart screen...");
        sendMcpRequest("tools/call", "{\"name\":\"generate_locators\",\"arguments\":{}}");
        Thread.sleep(3000);
    }
    
    /**
     * Phase 3: Generate test code with discovered locators
     */
    private static void generateTestCodeWithLocators() throws IOException, InterruptedException {
        logger.info("\n🧪 Phase 3: Generate Test Code with Locators");
        
        // Generate basic test code for product catalog
        logger.info("📝 Generating test code for product catalog functionality...");
        String catalogTestSteps = "[" +
            "\"Launch SauceLabs Demo App\"," +
            "\"Verify product catalog is displayed\"," +
            "\"Verify product items are visible\"," +
            "\"Click on Sauce Labs Backpack product\"," +
            "\"Verify product details page loads\"," +
            "\"Verify product name and price are displayed\"," +
            "\"Add product to cart\"," +
            "\"Verify cart badge updates to show 1 item\"" +
            "]";
        sendMcpRequest("tools/call", "{\"name\":\"appium_generate_tests\",\"arguments\":{\"steps\":" + catalogTestSteps + ",\"includeLocators\":true,\"testName\":\"ProductCatalogTest\"}}");
        Thread.sleep(5000);
        
        // Generate test code for shopping cart functionality
        logger.info("🛒 Generating test code for shopping cart functionality...");
        String cartTestSteps = "[" +
            "\"Navigate to shopping cart\"," +
            "\"Verify product is in cart\"," +
            "\"Verify product details in cart\"," +
            "\"Test quantity adjustment\"," +
            "\"Test remove from cart\"," +
            "\"Verify cart updates correctly\"" +
            "]";
        sendMcpRequest("tools/call", "{\"name\":\"appium_generate_tests\",\"arguments\":{\"steps\":" + cartTestSteps + ",\"includeLocators\":true,\"testName\":\"ShoppingCartTest\"}}");
        Thread.sleep(5000);
    }
    
    /**
     * Phase 4: Generate advanced test scenarios
     */
    private static void generateAdvancedTestScenarios() throws IOException, InterruptedException {
        logger.info("\n🚀 Phase 4: Generate Advanced Test Scenarios");
        
        // Navigate to menu for login testing
        logger.info("🍔 Opening hamburger menu for login testing...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_click\",\"arguments\":{\"strategy\":\"accessibility id\",\"selector\":\"open menu\"}}");
        Thread.sleep(3000);
        
        // Generate locators for menu
        sendMcpRequest("tools/call", "{\"name\":\"generate_locators\",\"arguments\":{}}");
        Thread.sleep(3000);
        
        // Navigate to login
        sendMcpRequest("tools/call", "{\"name\":\"appium_click\",\"arguments\":{\"strategy\":\"accessibility id\",\"selector\":\"menu item log in\"}}");
        Thread.sleep(3000);
        
        // Generate locators for login screen
        logger.info("🔐 Generating locators for login screen...");
        sendMcpRequest("tools/call", "{\"name\":\"generate_locators\",\"arguments\":{}}");
        Thread.sleep(3000);
        
        // Generate comprehensive login test code
        logger.info("🔐 Generating comprehensive login test code...");
        String loginTestSteps = "[" +
            "\"Open hamburger menu\"," +
            "\"Navigate to login screen\"," +
            "\"Verify login form is displayed\"," +
            "\"Test login with valid credentials\"," +
            "\"Verify successful login\"," +
            "\"Test login with invalid credentials\"," +
            "\"Verify error message is displayed\"," +
            "\"Test logout functionality\"," +
            "\"Verify user is logged out\"" +
            "]";
        sendMcpRequest("tools/call", "{\"name\":\"appium_generate_tests\",\"arguments\":{\"steps\":" + loginTestSteps + ",\"includeLocators\":true,\"testName\":\"LoginTest\",\"includeDataDriven\":true}}");
        Thread.sleep(5000);
        
        // Generate end-to-end test scenario
        logger.info("🎯 Generating end-to-end test scenario...");
        String e2eTestSteps = "[" +
            "\"Launch app and verify product catalog\"," +
            "\"Browse and select multiple products\"," +
            "\"Add products to cart\"," +
            "\"Navigate to cart and verify contents\"," +
            "\"Proceed to login\"," +
            "\"Complete login process\"," +
            "\"Return to cart and verify state\"," +
            "\"Test complete shopping flow\"" +
            "]";
        sendMcpRequest("tools/call", "{\"name\":\"appium_generate_tests\",\"arguments\":{\"steps\":" + e2eTestSteps + ",\"includeLocators\":true,\"testName\":\"EndToEndTest\",\"includePageObjects\":true}}");
        Thread.sleep(5000);
    }
    
    /**
     * Phase 5: Export generated code to files
     */
    private static void exportGeneratedCode() throws IOException, InterruptedException {
        logger.info("\n💾 Phase 5: Export Generated Code");
        
        // Request code export in different formats
        logger.info("📄 Exporting generated code to files...");
        
        // Export as JUnit test class
        sendMcpRequest("tools/call", "{\"name\":\"export_generated_code\",\"arguments\":{\"format\":\"junit\",\"outputPath\":\"generated-tests/SauceLabsTests.java\"}}");
        Thread.sleep(2000);
        
        // Export as TestNG test class
        sendMcpRequest("tools/call", "{\"name\":\"export_generated_code\",\"arguments\":{\"format\":\"testng\",\"outputPath\":\"generated-tests/SauceLabsTestNG.java\"}}");
        Thread.sleep(2000);
        
        // Export page object model
        sendMcpRequest("tools/call", "{\"name\":\"export_generated_code\",\"arguments\":{\"format\":\"pageobject\",\"outputPath\":\"generated-tests/pages/\"}}");
        Thread.sleep(2000);
        
        logger.info("✅ Code generation and export completed!");
        logger.info("📁 Generated files available in: generated-tests/");
    }
    
    private static void sendMcpRequest(String method, String params) throws IOException {
        String request = "{\"jsonrpc\":\"2.0\",\"id\":\"" + (requestId++) + "\",\"method\":\"" + method + "\",\"params\":" + params + "}";
        logger.debug("📤 Sending: {}", request);
        writer.write(request);
        writer.newLine();
        writer.flush();
    }
    
    private static void startOutputReaders(BufferedReader reader, BufferedReader errorReader) {
        // Output reader thread
        new Thread(() -> {
            try {
                String line;
                while ((line = reader.readLine()) != null) {
                    if (!line.trim().isEmpty()) {
                        // Filter out base64 screenshot data to keep logs clean
                        if (line.contains("\"screenshot\"") && line.length() > 1000) {
                            logger.info("📸 Screenshot captured (data filtered for readability)");
                        } else {
                            logger.info("📥 MCP Response: {}", line);
                        }
                    }
                }
            } catch (IOException e) {
                logger.error("Error reading MCP output", e);
            }
        }).start();
        
        // Error reader thread
        new Thread(() -> {
            try {
                String line;
                while ((line = errorReader.readLine()) != null) {
                    if (!line.trim().isEmpty()) {
                        logger.warn("⚠️ MCP Error: {}", line);
                    }
                }
            } catch (IOException e) {
                logger.error("Error reading MCP error stream", e);
            }
        }).start();
    }
}
