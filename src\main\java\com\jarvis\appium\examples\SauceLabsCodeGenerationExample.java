package com.jarvis.appium.examples;

import com.jarvis.appium.client.JarvisAppiumClient;
import com.jarvis.appium.config.ConfigManager;
import com.jarvis.appium.config.JarvisAppiumConfig;
import com.jarvis.appium.model.ElementLocator;
import com.jarvis.appium.model.Platform;
import com.jarvis.appium.model.SessionInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * SauceLabs Code Generation Example with Given-When-Then Scenarios
 *
 * Demonstrates the generate://code-with-locators functionality using BDD-style scenarios.
 * This example shows how to:
 * 1. Connect to the app and discover elements
 * 2. Generate intelligent locators
 * 3. Create test code from Given-When-Then scenarios using appium_generate_tests
 * 4. Export generated code for use in test frameworks
 */
public class SauceLabsCodeGenerationExample {
    
    private static final Logger logger = LoggerFactory.getLogger(SauceLabsCodeGenerationExample.class);
    
    // SauceLabs Sample App Configuration
    private static final String APK_PATH = "C:\\POC\\AI\\MCP Integration Server\\mcp-client-integration\\Android.SauceLabs.Mobile.Sample.app.2.7.1.apk";
    private static final String PACKAGE_NAME = "com.saucelabs.mydemoapp.android";
    private static final String ACTIVITY_NAME = ".view.activities.SplashActivity";
    
    private static BufferedWriter writer;
    private static int requestId = 1;
    
    public static void main(String[] args) {
        try {
            demonstrateCodeGenerationWithLocators();
        } catch (Exception e) {
            logger.error("SauceLabs code generation example failed", e);
        }
    }
    
    /**
     * Main demonstration of generate://code-with-locators functionality using Given-When-Then scenarios
     */
    private static void demonstrateCodeGenerationWithLocators() throws IOException, InterruptedException {
        logger.info("=== 🧪 SauceLabs Code Generation with Given-When-Then Scenarios ===");
        logger.info("📱 App: SauceLabs Demo App");
        logger.info("🎯 Goal: Generate test code from BDD scenarios using appium_generate_tests");
        
        String[] command = {"node", "C:\\POC\\AI\\MCP Integration Server\\jarvis-appium\\dist\\index.js"};
        
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.redirectErrorStream(false);
        
        Process process = processBuilder.start();
        
        writer = new BufferedWriter(new OutputStreamWriter(process.getOutputStream()));
        BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
        
        // Start threads to read output
        startOutputReaders(reader, errorReader);
        Thread.sleep(3000);
        
        try {
            // Phase 1: Initialize and setup
            initializeAndSetup();

            // Phase 2: Discover elements and generate locators
            discoverElementsAndGenerateLocators();

            // Phase 3: Generate test code from Given-When-Then scenarios
            generateTestCodeFromGherkinScenarios();

            // Phase 4: Generate advanced BDD test scenarios
            generateAdvancedBDDScenarios();

            // Phase 5: Export generated code
            exportGeneratedCode();
            
        } finally {
            // Cleanup
            logger.info("🧹 Cleaning up...");
            writer.close();
            reader.close();
            errorReader.close();
            process.destroy();
            if (!process.waitFor(5, TimeUnit.SECONDS)) {
                process.destroyForcibly();
            }
        }
        
        logger.info("✅ Code generation example completed successfully!");
    }
    
    /**
     * Phase 1: Initialize MCP connection and create session
     */
    private static void initializeAndSetup() throws IOException, InterruptedException {
        logger.info("\n🔧 Phase 1: Initialize and Setup");
        
        // Initialize MCP
        logger.info("🔗 Initializing MCP connection...");
        sendMcpRequest("initialize", "{\"protocolVersion\":\"2024-11-05\",\"clientInfo\":{\"name\":\"saucelabs-codegen\",\"version\":\"1.0.0\"},\"capabilities\":{\"roots\":{\"listChanged\":true},\"tools\":{\"listChanged\":true}}}");
        Thread.sleep(2000);
        
        // Select platform
        logger.info("📱 Selecting Android platform...");
        sendMcpRequest("tools/call", "{\"name\":\"select_platform\",\"arguments\":{\"platform\":\"android\"}}");
        Thread.sleep(2000);
        
        // Create session with SauceLabs app
        logger.info("🚀 Creating session with SauceLabs Demo App...");
        String sessionCapabilities = String.format(
            "{\"platform\":\"android\",\"capabilities\":{" +
            "\"platformName\":\"Android\"," +
            "\"app\":\"" + APK_PATH.replace("\\", "\\\\") + "\"," +
            "\"appPackage\":\"" + PACKAGE_NAME + "\"," +
            "\"appActivity\":\"" + ACTIVITY_NAME + "\"," +
            "\"automationName\":\"UiAutomator2\"," +
            "\"newCommandTimeout\":300," +
            "\"autoGrantPermissions\":true," +
            "\"noReset\":false" +
            "}}"
        );
        sendMcpRequest("tools/call", "{\"name\":\"create_session\",\"arguments\":" + sessionCapabilities + "}");
        Thread.sleep(8000); // Wait for app to launch
        
        // Take initial screenshot
        logger.info("📸 Taking initial screenshot...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(2000);
    }
    
    /**
     * Phase 2: Discover elements and generate intelligent locators
     */
    private static void discoverElementsAndGenerateLocators() throws IOException, InterruptedException {
        logger.info("\n🔍 Phase 2: Element Discovery and Locator Generation");
        
        // Generate locators for product catalog screen
        logger.info("🎯 Generating locators for product catalog screen...");
        sendMcpRequest("tools/call", "{\"name\":\"generate_locators\",\"arguments\":{}}");
        Thread.sleep(3000);
        
        // Navigate to product details to discover more elements
        logger.info("🛍️ Navigating to product details for more element discovery...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"accessibility id\",\"selector\":\"Sauce Labs Backpack\"}}");
        Thread.sleep(2000);
        
        // Click on first product
        sendMcpRequest("tools/call", "{\"name\":\"appium_click\",\"arguments\":{\"strategy\":\"accessibility id\",\"selector\":\"Sauce Labs Backpack\"}}");
        Thread.sleep(3000);
        
        // Generate locators for product details screen
        logger.info("📋 Generating locators for product details screen...");
        sendMcpRequest("tools/call", "{\"name\":\"generate_locators\",\"arguments\":{}}");
        Thread.sleep(3000);
        
        // Take screenshot of product details
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(2000);
        
        // Navigate to cart to discover cart elements
        logger.info("🛒 Adding to cart and discovering cart elements...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_click\",\"arguments\":{\"strategy\":\"accessibility id\",\"selector\":\"Add To Cart button\"}}");
        Thread.sleep(2000);
        
        // Click cart icon
        sendMcpRequest("tools/call", "{\"name\":\"appium_click\",\"arguments\":{\"strategy\":\"accessibility id\",\"selector\":\"cart badge\"}}");
        Thread.sleep(3000);
        
        // Generate locators for cart screen
        logger.info("🛒 Generating locators for shopping cart screen...");
        sendMcpRequest("tools/call", "{\"name\":\"generate_locators\",\"arguments\":{}}");
        Thread.sleep(3000);
    }
    
    /**
     * Phase 3: Generate test code from Given-When-Then scenarios
     */
    private static void generateTestCodeFromGherkinScenarios() throws IOException, InterruptedException {
        logger.info("\n🧪 Phase 3: Generate Test Code from Given-When-Then Scenarios");

        // Scenario 1: Product Catalog Browsing
        logger.info("📝 Generating test code for Product Catalog scenario...");
        String productCatalogScenario =
            "Scenario: Browse Product Catalog\\n" +
            "Given the SauceLabs app is launched\\n" +
            "When I view the product catalog\\n" +
            "And I click on Sauce Labs Backpack\\n" +
            "Then I should see product details\\n" +
            "And I should see the product price\\n" +
            "And I should see the Add To Cart button";

        sendMcpRequest("tools/call", "{\"name\":\"appium_generate_tests\",\"arguments\":{\"scenario\":\"" + productCatalogScenario + "\",\"includeLocators\":true,\"testName\":\"ProductCatalogTest\"}}");
        Thread.sleep(5000);

        // Scenario 2: Add Product to Cart
        logger.info("🛒 Generating test code for Add to Cart scenario...");
        String addToCartScenario =
            "Scenario: Add Product to Cart\\n" +
            "Given the SauceLabs app is launched\\n" +
            "When I click on Sauce Labs Backpack\\n" +
            "And I click the Add To Cart button\\n" +
            "Then the cart badge should show 1 item\\n" +
            "And I should see the cart icon updated";

        sendMcpRequest("tools/call", "{\"name\":\"appium_generate_tests\",\"arguments\":{\"scenario\":\"" + addToCartScenario + "\",\"includeLocators\":true,\"testName\":\"AddToCartTest\"}}");
        Thread.sleep(5000);

        // Scenario 3: Shopping Cart Operations
        logger.info("�️ Generating test code for Shopping Cart scenario...");
        String shoppingCartScenario =
            "Scenario: Manage Shopping Cart\\n" +
            "Given I have added Sauce Labs Backpack to cart\\n" +
            "When I click on cart badge\\n" +
            "And I view the cart contents\\n" +
            "Then I should see the product in cart\\n" +
            "And I should see the correct quantity\\n" +
            "And I should see the total price";

        sendMcpRequest("tools/call", "{\"name\":\"appium_generate_tests\",\"arguments\":{\"scenario\":\"" + shoppingCartScenario + "\",\"includeLocators\":true,\"testName\":\"ShoppingCartTest\"}}");
        Thread.sleep(5000);
    }
    
    /**
     * Phase 4: Generate advanced BDD test scenarios
     */
    private static void generateAdvancedBDDScenarios() throws IOException, InterruptedException {
        logger.info("\n🚀 Phase 4: Generate Advanced BDD Test Scenarios");

        // Navigate to menu for login testing
        logger.info("🍔 Opening hamburger menu for login testing...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_click\",\"arguments\":{\"strategy\":\"accessibility id\",\"selector\":\"open menu\"}}");
        Thread.sleep(3000);

        // Generate locators for menu
        sendMcpRequest("tools/call", "{\"name\":\"generate_locators\",\"arguments\":{}}");
        Thread.sleep(3000);

        // Navigate to login
        sendMcpRequest("tools/call", "{\"name\":\"appium_click\",\"arguments\":{\"strategy\":\"accessibility id\",\"selector\":\"menu item log in\"}}");
        Thread.sleep(3000);

        // Generate locators for login screen
        logger.info("🔐 Generating locators for login screen...");
        sendMcpRequest("tools/call", "{\"name\":\"generate_locators\",\"arguments\":{}}");
        Thread.sleep(3000);

        // Scenario 4: Valid Login
        logger.info("🔐 Generating test code for Valid Login scenario...");
        String validLoginScenario =
            "Scenario: Valid User Login\\n" +
            "Given the SauceLabs app is launched\\n" +
            "When I open the menu\\n" +
            "And I navigate to login\\n" +
            "And I enter username 'standard_user'\\n" +
            "And I enter password 'secret_sauce'\\n" +
            "And I click the login button\\n" +
            "Then I should be logged in successfully\\n" +
            "And I should see the products page";

        sendMcpRequest("tools/call", "{\"name\":\"appium_generate_tests\",\"arguments\":{\"scenario\":\"" + validLoginScenario + "\",\"includeLocators\":true,\"testName\":\"ValidLoginTest\"}}");
        Thread.sleep(5000);

        // Scenario 5: Invalid Login
        logger.info("❌ Generating test code for Invalid Login scenario...");
        String invalidLoginScenario =
            "Scenario: Invalid User Login\\n" +
            "Given the SauceLabs app is launched\\n" +
            "When I open the menu\\n" +
            "And I navigate to login\\n" +
            "And I enter username 'invalid_user'\\n" +
            "And I enter password 'wrong_password'\\n" +
            "And I click the login button\\n" +
            "Then I should see an error message\\n" +
            "And I should remain on the login screen";

        sendMcpRequest("tools/call", "{\"name\":\"appium_generate_tests\",\"arguments\":{\"scenario\":\"" + invalidLoginScenario + "\",\"includeLocators\":true,\"testName\":\"InvalidLoginTest\"}}");
        Thread.sleep(5000);

        // Scenario 6: End-to-End Shopping Flow
        logger.info("🎯 Generating test code for End-to-End Shopping scenario...");
        String e2eShoppingScenario =
            "Scenario: Complete Shopping Flow\\n" +
            "Given the SauceLabs app is launched\\n" +
            "When I click on Sauce Labs Backpack\\n" +
            "And I click the Add To Cart button\\n" +
            "And I click on Sauce Labs Bike Light\\n" +
            "And I click the Add To Cart button\\n" +
            "And I click on cart badge\\n" +
            "Then I should see 2 items in cart\\n" +
            "And I should see the correct total price\\n" +
            "And I should be able to proceed to checkout";

        sendMcpRequest("tools/call", "{\"name\":\"appium_generate_tests\",\"arguments\":{\"scenario\":\"" + e2eShoppingScenario + "\",\"includeLocators\":true,\"testName\":\"EndToEndShoppingTest\",\"includePageObjects\":true}}");
        Thread.sleep(5000);

        // Scenario 7: Product Sorting
        logger.info("📊 Generating test code for Product Sorting scenario...");
        String sortingScenario =
            "Scenario: Sort Products\\n" +
            "Given the SauceLabs app is launched\\n" +
            "When I click the sort button\\n" +
            "And I select sort by name A to Z\\n" +
            "Then products should be sorted alphabetically\\n" +
            "And Sauce Labs Backpack should appear first";

        sendMcpRequest("tools/call", "{\"name\":\"appium_generate_tests\",\"arguments\":{\"scenario\":\"" + sortingScenario + "\",\"includeLocators\":true,\"testName\":\"ProductSortingTest\"}}");
        Thread.sleep(5000);
    }
    
    /**
     * Phase 5: Export generated BDD test code to files
     */
    private static void exportGeneratedCode() throws IOException, InterruptedException {
        logger.info("\n💾 Phase 5: Export Generated BDD Test Code");

        // Request code export in different formats
        logger.info("📄 Exporting generated BDD test code to files...");

        // Export as JUnit test class with BDD scenarios
        sendMcpRequest("tools/call", "{\"name\":\"export_generated_code\",\"arguments\":{\"format\":\"junit\",\"outputPath\":\"generated-tests/SauceLabsBDDTests.java\",\"includeBDD\":true}}");
        Thread.sleep(2000);

        // Export as TestNG test class with BDD scenarios
        sendMcpRequest("tools/call", "{\"name\":\"export_generated_code\",\"arguments\":{\"format\":\"testng\",\"outputPath\":\"generated-tests/SauceLabsBDDTestNG.java\",\"includeBDD\":true}}");
        Thread.sleep(2000);

        // Export Cucumber feature files
        sendMcpRequest("tools/call", "{\"name\":\"export_generated_code\",\"arguments\":{\"format\":\"cucumber\",\"outputPath\":\"generated-tests/features/SauceLabs.feature\"}}");
        Thread.sleep(2000);

        // Export page object model with BDD support
        sendMcpRequest("tools/call", "{\"name\":\"export_generated_code\",\"arguments\":{\"format\":\"pageobject\",\"outputPath\":\"generated-tests/pages/\",\"includeBDD\":true}}");
        Thread.sleep(2000);

        // Export step definitions for Cucumber
        sendMcpRequest("tools/call", "{\"name\":\"export_generated_code\",\"arguments\":{\"format\":\"stepdefinitions\",\"outputPath\":\"generated-tests/steps/SauceLabsSteps.java\"}}");
        Thread.sleep(2000);

        logger.info("✅ BDD code generation and export completed!");
        logger.info("📁 Generated files available in: generated-tests/");
        logger.info("🥒 Cucumber features: generated-tests/features/");
        logger.info("🔧 Step definitions: generated-tests/steps/");
        logger.info("📄 Page objects: generated-tests/pages/");
    }
    
    private static void sendMcpRequest(String method, String params) throws IOException {
        String request = "{\"jsonrpc\":\"2.0\",\"id\":\"" + (requestId++) + "\",\"method\":\"" + method + "\",\"params\":" + params + "}";
        logger.debug("📤 Sending: {}", request);
        writer.write(request);
        writer.newLine();
        writer.flush();
    }
    
    private static void startOutputReaders(BufferedReader reader, BufferedReader errorReader) {
        // Output reader thread
        new Thread(() -> {
            try {
                String line;
                while ((line = reader.readLine()) != null) {
                    if (!line.trim().isEmpty()) {
                        // Filter out base64 screenshot data to keep logs clean
                        if (line.contains("\"screenshot\"") && line.length() > 1000) {
                            logger.info("📸 Screenshot captured (data filtered for readability)");
                        } else {
                            logger.info("📥 MCP Response: {}", line);
                        }
                    }
                }
            } catch (IOException e) {
                logger.error("Error reading MCP output", e);
            }
        }).start();
        
        // Error reader thread
        new Thread(() -> {
            try {
                String line;
                while ((line = errorReader.readLine()) != null) {
                    if (!line.trim().isEmpty()) {
                        logger.warn("⚠️ MCP Error: {}", line);
                    }
                }
            } catch (IOException e) {
                logger.error("Error reading MCP error stream", e);
            }
        }).start();
    }
}
