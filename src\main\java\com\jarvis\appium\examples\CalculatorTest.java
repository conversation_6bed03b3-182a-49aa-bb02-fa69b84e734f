package com.jarvis.appium.examples;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.concurrent.TimeUnit;

/**
 * Test that opens Calculator app and performs calculations - VERY VISIBLE changes!
 */
public class CalculatorTest {
    
    private static final Logger logger = LoggerFactory.getLogger(CalculatorTest.class);
    
    private static BufferedWriter writer;
    private static BufferedReader reader;
    private static BufferedReader errorReader;
    
    public static void main(String[] args) {
        try {
            testCalculatorApp();
        } catch (Exception e) {
            logger.error("Calculator test failed", e);
        }
    }
    
    private static void testCalculatorApp() throws IOException, InterruptedException {
        logger.info("=== 🧮 CALCULATOR APP TEST ===");
        logger.info("🎬 This will open Calculator and perform math - VERY VISIBLE!");
        
        // Start MCP server
        String[] command = {"node", "dist/index.js"};
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.directory(new File("C:\\POC\\AI\\MCP Integration Server\\jarvis-appium"));
        processBuilder.redirectErrorStream(false);
        
        logger.info("🚀 Starting MCP server...");
        Process process = processBuilder.start();
        
        writer = new BufferedWriter(new OutputStreamWriter(process.getOutputStream()));
        reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
        
        // Start output readers
        startOutputReaders();
        Thread.sleep(3000);
        
        // Initialize MCP
        logger.info("🔗 Initializing MCP connection...");
        sendMcpRequest("initialize", "{\"protocolVersion\":\"2024-11-05\",\"clientInfo\":{\"name\":\"calculator-test\",\"version\":\"1.0.0\"},\"capabilities\":{}}");
        Thread.sleep(2000);
        
        // Select Android platform
        logger.info("📱 Selecting Android platform...");
        sendMcpRequest("tools/call", "{\"name\":\"select_platform\",\"arguments\":{\"platform\":\"android\"}}");
        Thread.sleep(2000);
        
        // Create session with Calculator app
        logger.info("🎯 Creating Android session with Calculator app...");
        String sessionCapabilities = "{\"platform\":\"android\",\"capabilities\":{\"platformName\":\"Android\",\"deviceName\":\"emulator-5554\",\"automationName\":\"UiAutomator2\",\"appPackage\":\"com.google.android.calculator\",\"appActivity\":\"com.android.calculator2.Calculator\"}}";
        sendMcpRequest("tools/call", "{\"name\":\"create_session\",\"arguments\":" + sessionCapabilities + "}");
        Thread.sleep(10000);
        
        logger.info("📸 Taking screenshot of Calculator app...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(3000);
        
        // Click number 5
        logger.info("🔢 CLICKING NUMBER 5 - WATCH THE CALCULATOR!");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"id\",\"selector\":\"com.google.android.calculator:id/digit_5\"}}");
        Thread.sleep(2000);
        
        // Click the found element (we'll use a generic approach)
        logger.info("👆 Clicking the 5 button...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@text='5']\"}}");
        Thread.sleep(2000);
        
        // Click plus button
        logger.info("➕ CLICKING PLUS BUTTON - WATCH THE CALCULATOR!");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@text='+']\"}}");
        Thread.sleep(2000);
        
        // Click number 3
        logger.info("🔢 CLICKING NUMBER 3 - WATCH THE CALCULATOR!");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@text='3']\"}}");
        Thread.sleep(2000);
        
        // Click equals button
        logger.info("🟰 CLICKING EQUALS BUTTON - WATCH FOR RESULT!");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@text='=']\"}}");
        Thread.sleep(3000);
        
        logger.info("📸 Taking final screenshot showing result...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(3000);
        
        // Try to get the result text
        logger.info("📖 Reading the calculation result...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"id\",\"selector\":\"com.google.android.calculator:id/result_final\"}}");
        Thread.sleep(2000);
        
        logger.info("✅ CALCULATOR TEST COMPLETED!");
        logger.info("🧮 You should have seen: 5 + 3 = 8 on your calculator!");
        
        // Clean up
        logger.info("🧹 Cleaning up...");
        writer.close();
        reader.close();
        errorReader.close();
        process.destroy();
    }
    
    private static void sendMcpRequest(String method, String params) throws IOException {
        String request = "{\"jsonrpc\":\"2.0\",\"id\":\"" + System.currentTimeMillis() + "\",\"method\":\"" + method + "\",\"params\":" + params + "}";
        logger.info("📤 SENDING: {}", request);
        writer.write(request);
        writer.newLine();
        writer.flush();
    }
    
    private static void startOutputReaders() {
        Thread outputThread = new Thread(() -> {
            try {
                String line;
                while ((line = reader.readLine()) != null) {
                    // Show important responses but filter out base64 screenshot data
                    if (line.contains("Element id") || line.contains("session created") || line.contains("clicked")) {
                        logger.info("📥 SERVER: {}", line);
                    } else if (line.contains("screenshot")) {
                        // Don't log screenshot responses as they contain large base64 data
                        logger.info("📸 Screenshot taken successfully");
                    } else if (line.contains("\"result\"") && !line.contains("data:image") && line.length() < 500) {
                        // Only log short result messages, avoid base64 data
                        logger.info("📥 SERVER: {}", line);
                    }
                }
            } catch (IOException e) {
                // Expected when process ends
            }
        });
        outputThread.setDaemon(true);
        outputThread.start();
        
        Thread errorThread = new Thread(() -> {
            try {
                String line;
                while ((line = errorReader.readLine()) != null) {
                    // Only show important errors
                    if (line.contains("ERROR") && !line.contains("DEBUG") && !line.contains("dbug")) {
                        logger.error("⚠️ SERVER ERROR: {}", line);
                    }
                }
            } catch (IOException e) {
                // Expected when process ends
            }
        });
        errorThread.setDaemon(true);
        errorThread.start();
    }
}
