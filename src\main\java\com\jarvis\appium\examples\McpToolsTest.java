package com.jarvis.appium.examples;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.concurrent.TimeUnit;

/**
 * Test MCP server tools without requiring actual devices
 */
public class McpToolsTest {
    
    private static final Logger logger = LoggerFactory.getLogger(McpToolsTest.class);
    
    private static BufferedWriter writer;
    private static BufferedReader reader;
    private static BufferedReader errorReader;
    
    public static void main(String[] args) {
        try {
            testMcpTools();
        } catch (Exception e) {
            logger.error("MCP tools test failed", e);
        }
    }
    
    private static void testMcpTools() throws IOException, InterruptedException {
        logger.info("=== 🧪 MCP Tools Test (No Device Required) ===");
        
        // Start MCP server
        String[] command = {"node", "dist/index.js"};
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.directory(new File("C:\\POC\\AI\\MCP Integration Server\\jarvis-appium"));
        processBuilder.redirectErrorStream(false);
        
        logger.info("🚀 Starting MCP server...");
        Process process = processBuilder.start();
        
        writer = new BufferedWriter(new OutputStreamWriter(process.getOutputStream()));
        reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
        
        // Start output readers
        startOutputReaders();
        Thread.sleep(3000);
        
        // Test 1: Initialize MCP
        logger.info("🔗 Test 1: Initializing MCP connection...");
        sendMcpRequest("initialize", "{\"protocolVersion\":\"2024-11-05\",\"clientInfo\":{\"name\":\"tools-test\",\"version\":\"1.0.0\"},\"capabilities\":{}}");
        Thread.sleep(2000);
        
        // Test 2: List tools
        logger.info("🛠️ Test 2: Listing available tools...");
        sendMcpRequest("tools/list", "{}");
        Thread.sleep(2000);
        
        // Test 3: Select platform (this should work without device)
        logger.info("📱 Test 3: Selecting Android platform...");
        sendMcpRequest("tools/call", "{\"name\":\"select_platform\",\"arguments\":{\"platform\":\"android\"}}");
        Thread.sleep(2000);
        
        // Test 4: Query documentation (this should work without device)
        logger.info("📚 Test 4: Querying Appium documentation...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_documentation_query\",\"arguments\":{\"query\":\"How to find elements in Appium?\"}}");
        Thread.sleep(3000);
        
        // Test 5: Try to create session (this will fail but we can see the error)
        logger.info("🎯 Test 5: Attempting to create session (expected to fail without device)...");
        String sessionCapabilities = "{\"platform\":\"android\",\"capabilities\":{\"platformName\":\"Android\",\"deviceName\":\"emulator-5554\",\"automationName\":\"UiAutomator2\"}}";
        sendMcpRequest("tools/call", "{\"name\":\"create_session\",\"arguments\":" + sessionCapabilities + "}");
        Thread.sleep(10000);
        
        // Clean up
        logger.info("🧹 Cleaning up...");
        writer.close();
        reader.close();
        errorReader.close();
        process.destroy();
        
        logger.info("✅ MCP tools test completed");
    }
    
    private static void sendMcpRequest(String method, String params) throws IOException {
        String request = "{\"jsonrpc\":\"2.0\",\"id\":\"" + System.currentTimeMillis() + "\",\"method\":\"" + method + "\",\"params\":" + params + "}";
        logger.info("📤 SENDING: {}", request);
        writer.write(request);
        writer.newLine();
        writer.flush();
    }
    
    private static void startOutputReaders() {
        Thread outputThread = new Thread(() -> {
            try {
                String line;
                while ((line = reader.readLine()) != null) {
                    // Filter out base64 screenshot data and only show important messages
                    if (line.contains("screenshot") && line.contains("data:image")) {
                        logger.info("📸 Screenshot taken successfully");
                    } else if (line.length() < 1000) {
                        // Only log messages shorter than 1000 characters to avoid base64 data
                        logger.info("📥 SERVER OUTPUT: {}", line);
                    } else {
                        logger.info("📥 SERVER: Large response received ({}+ chars)", line.length());
                    }
                }
            } catch (IOException e) {
                // Expected when process ends
            }
        });
        outputThread.setDaemon(true);
        outputThread.start();

        Thread errorThread = new Thread(() -> {
            try {
                String line;
                while ((line = errorReader.readLine()) != null) {
                    // Only show important errors, not debug messages
                    if (line.contains("ERROR") && !line.contains("DEBUG")) {
                        logger.error("⚠️ SERVER ERROR: {}", line);
                    }
                }
            } catch (IOException e) {
                // Expected when process ends
            }
        });
        errorThread.setDaemon(true);
        errorThread.start();
    }
}
