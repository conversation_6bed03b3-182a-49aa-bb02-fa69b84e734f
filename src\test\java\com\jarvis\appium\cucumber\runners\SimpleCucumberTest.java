package com.jarvis.appium.cucumber.runners;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Simple test to verify Cucumber setup is working
 */
public class SimpleCucumberTest {

    @Test
    @DisplayName("Verify Cucumber dependencies are available")
    public void testCucumberDependencies() {
        // Test that Cucumber classes can be loaded
        try {
            Class.forName("io.cucumber.java.en.Given");
            Class.forName("io.cucumber.java.en.When");
            Class.forName("io.cucumber.java.en.Then");
            Class.forName("io.cucumber.java.en.And");
            Class.forName("io.cucumber.junit.platform.engine.Constants");

            System.out.println("✅ All Cucumber dependencies are available");
            assertTrue(true, "Cucumber dependencies loaded successfully");

        } catch (ClassNotFoundException e) {
            fail("Cucumber dependency not found: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("Verify test configuration classes")
    public void testConfigurationClasses() {
        try {
            Class.forName("com.jarvis.appium.cucumber.config.TestConfig");
            Class.forName("com.jarvis.appium.cucumber.utils.McpUtils");
            Class.forName("com.jarvis.appium.cucumber.steps.SingleScenarioSteps");
            
            System.out.println("✅ All configuration classes are available");
            assertTrue(true, "Configuration classes loaded successfully");
            
        } catch (ClassNotFoundException e) {
            fail("Configuration class not found: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("Verify feature file exists")
    public void testFeatureFileExists() {
        // Check if feature file exists
        java.io.InputStream featureFile = getClass().getClassLoader()
            .getResourceAsStream("features/SingleScenario.feature");
        
        assertNotNull(featureFile, "SingleScenario.feature should exist in resources/features/");
        System.out.println("✅ Feature file found");
        
        try {
            featureFile.close();
        } catch (Exception e) {
            // Ignore close errors
        }
    }

    @Test
    @DisplayName("Verify cucumber properties exists")
    public void testCucumberPropertiesExists() {
        // Check if cucumber.properties exists
        java.io.InputStream propsFile = getClass().getClassLoader()
            .getResourceAsStream("cucumber.properties");
        
        assertNotNull(propsFile, "cucumber.properties should exist in resources/");
        System.out.println("✅ Cucumber properties file found");
        
        try {
            propsFile.close();
        } catch (Exception e) {
            // Ignore close errors
        }
    }
}
