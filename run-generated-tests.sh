#!/bin/bash

# 🧪 Run Generated Tests Script
# This script demonstrates how to use the generated test code from the SauceLabs example

echo "🚀 SauceLabs Generated Tests Runner"
echo "=================================="

# Check if generated tests directory exists
if [ ! -d "generated-tests" ]; then
    echo "❌ Generated tests directory not found!"
    echo "📝 Please run the code generation example first:"
    echo "   mvn exec:java -Dexec.mainClass=\"com.jarvis.appium.examples.SauceLabsCodeGenerationExample\""
    exit 1
fi

echo "✅ Generated tests directory found"

# Check for Java files
JAVA_FILES=$(find generated-tests -name "*.java" | wc -l)
if [ $JAVA_FILES -eq 0 ]; then
    echo "❌ No Java test files found in generated-tests/"
    echo "📝 Please run the code generation example to create test files"
    exit 1
fi

echo "✅ Found $JAVA_FILES generated Java test files"

# Create a temporary Maven project structure for the generated tests
echo "🔧 Setting up temporary test project..."

# Create directories
mkdir -p temp-test-project/src/main/java
mkdir -p temp-test-project/src/test/java

# Copy generated tests
cp -r generated-tests/* temp-test-project/src/test/java/

# Create a simple pom.xml for the generated tests
cat > temp-test-project/pom.xml << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    
    <groupId>com.saucelabs.generated</groupId>
    <artifactId>generated-tests</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>
    
    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    
    <dependencies>
        <!-- Appium Java Client -->
        <dependency>
            <groupId>io.appium</groupId>
            <artifactId>java-client</artifactId>
            <version>8.6.0</version>
        </dependency>
        
        <!-- Selenium WebDriver -->
        <dependency>
            <groupId>org.seleniumhq.selenium</groupId>
            <artifactId>selenium-java</artifactId>
            <version>4.15.0</version>
        </dependency>
        
        <!-- JUnit 5 -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>5.10.0</version>
            <scope>test</scope>
        </dependency>
        
        <!-- TestNG (alternative) -->
        <dependency>
            <groupId>org.testng</groupId>
            <artifactId>testng</artifactId>
            <version>7.8.0</version>
            <scope>test</scope>
        </dependency>
        
        <!-- Logging -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-simple</artifactId>
            <version>2.0.9</version>
        </dependency>
    </dependencies>
    
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.2.2</version>
                <configuration>
                    <includes>
                        <include>**/*Test.java</include>
                        <include>**/*Tests.java</include>
                    </includes>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
EOF

echo "✅ Temporary test project created"

# Show what was generated
echo ""
echo "📋 Generated Test Structure:"
echo "============================"
find temp-test-project/src/test/java -name "*.java" | while read file; do
    echo "📄 $(basename "$file")"
    # Show first few lines to give an idea of content
    echo "   $(head -n 3 "$file" | tail -n 1 | sed 's/^[[:space:]]*/   /')"
done

echo ""
echo "🎯 Next Steps:"
echo "=============="
echo "1. 📱 Ensure your Android device/emulator is connected"
echo "2. 🚀 Start the MCP server: npx jarvis-appium"
echo "3. 🧪 Run the generated tests:"
echo "   cd temp-test-project"
echo "   mvn test"
echo ""
echo "4. 📊 View test results in: temp-test-project/target/surefire-reports/"
echo ""
echo "🔧 Customization:"
echo "================"
echo "• Edit generated test files in: temp-test-project/src/test/java/"
echo "• Modify test data and configurations as needed"
echo "• Add additional test scenarios"
echo "• Integrate with your CI/CD pipeline"
echo ""
echo "🧹 Cleanup:"
echo "==========="
echo "• Remove temp-test-project/ when done"
echo "• Keep generated-tests/ for reference"

# Optional: Compile the tests to check for syntax errors
echo ""
echo "🔍 Validating generated code..."
cd temp-test-project
if mvn compile test-compile > /dev/null 2>&1; then
    echo "✅ Generated code compiles successfully!"
else
    echo "⚠️  Generated code has compilation issues"
    echo "📝 Check the generated files and dependencies"
fi

cd ..

echo ""
echo "🎉 Generated tests are ready to use!"
echo "📁 Location: temp-test-project/src/test/java/"
